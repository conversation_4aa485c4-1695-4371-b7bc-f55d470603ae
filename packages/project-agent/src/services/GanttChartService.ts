import { Project } from '../models/Project';
import { Task } from '../models/Task';
import { logger } from '../utils/logger';
import * as d3 from 'd3';
import { createCanvas } from 'canvas';

// 甘特图数据接口
interface GanttTask {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  duration: number;
  progress: number;
  dependencies: string[];
  assignee?: string;
  priority: string;
  status: string;
  parent?: string;
  children?: GanttTask[];
  level: number;
  type: 'task' | 'milestone' | 'phase';
}

interface GanttChart {
  tasks: GanttTask[];
  timeline: {
    start: Date;
    end: Date;
    duration: number;
  };
  criticalPath: string[];
  metadata: {
    projectId: string;
    generatedAt: Date;
    totalTasks: number;
    completedTasks: number;
    overdueTasks: number;
  };
}

interface GanttOptions {
  includeWeekends?: boolean;
  showCriticalPath?: boolean;
  showDependencies?: boolean;
  groupByPhase?: boolean;
  timeUnit?: 'day' | 'week' | 'month';
  dateFormat?: string;
}

export class GanttChartService {
  // 生成甘特图数据
  async generateGanttChart(projectId: string, options: GanttOptions = {}): Promise<GanttChart> {
    try {
      logger.info('Generating Gantt chart', { projectId, options });

      // 获取项目和任务数据
      const project = await Project.query()
        .findById(projectId)
        .withGraphFetched('tasks');

      if (!project) {
        throw new Error(`Project ${projectId} not found`);
      }

      const tasks = await Task.query()
        .where('project_id', projectId)
        .orderBy('start_date', 'asc');

      // 转换为甘特图任务格式
      const ganttTasks = await this.convertToGanttTasks(project, tasks, options);

      // 计算时间线
      const timeline = this.calculateTimeline(ganttTasks);

      // 计算关键路径
      const criticalPath = options.showCriticalPath 
        ? this.calculateCriticalPath(ganttTasks)
        : [];

      // 生成元数据
      const metadata = {
        projectId,
        generatedAt: new Date(),
        totalTasks: tasks.length,
        completedTasks: tasks.filter(t => t.status === 'completed').length,
        overdueTasks: tasks.filter(t => t.status !== 'completed' && new Date() > t.dueDate).length
      };

      const ganttChart: GanttChart = {
        tasks: ganttTasks,
        timeline,
        criticalPath,
        metadata
      };

      logger.info('Gantt chart generated successfully', { 
        projectId, 
        taskCount: ganttTasks.length,
        timelineStart: timeline.start,
        timelineEnd: timeline.end
      });

      return ganttChart;
    } catch (error) {
      logger.error('Failed to generate Gantt chart', { error, projectId });
      throw error;
    }
  }

  // 生成甘特图SVG
  async generateGanttSVG(projectId: string, options: GanttOptions = {}): Promise<string> {
    try {
      const ganttChart = await this.generateGanttChart(projectId, options);
      return this.renderGanttSVG(ganttChart, options);
    } catch (error) {
      logger.error('Failed to generate Gantt SVG', { error, projectId });
      throw error;
    }
  }

  // 生成甘特图PNG
  async generateGanttPNG(projectId: string, options: GanttOptions = {}): Promise<Buffer> {
    try {
      const svg = await this.generateGanttSVG(projectId, options);
      return this.convertSVGToPNG(svg);
    } catch (error) {
      logger.error('Failed to generate Gantt PNG', { error, projectId });
      throw error;
    }
  }

  // 更新任务进度
  async updateTaskProgress(taskId: string, progress: number): Promise<void> {
    try {
      await Task.query()
        .findById(taskId)
        .patch({ progress });

      logger.info('Task progress updated', { taskId, progress });
    } catch (error) {
      logger.error('Failed to update task progress', { error, taskId, progress });
      throw error;
    }
  }

  // 重新安排任务
  async rescheduleTask(taskId: string, newStartDate: Date, newEndDate: Date): Promise<void> {
    try {
      const task = await Task.query().findById(taskId);
      if (!task) {
        throw new Error(`Task ${taskId} not found`);
      }

      // 更新任务日期
      await task.$query().patch({
        startDate: newStartDate,
        dueDate: newEndDate,
        updatedAt: new Date()
      });

      // 检查并更新依赖任务
      await this.updateDependentTasks(task);

      logger.info('Task rescheduled', { taskId, newStartDate, newEndDate });
    } catch (error) {
      logger.error('Failed to reschedule task', { error, taskId });
      throw error;
    }
  }

  // 添加任务依赖
  async addTaskDependency(taskId: string, dependsOnTaskId: string, type: string = 'finish-to-start'): Promise<void> {
    try {
      const task = await Task.query().findById(taskId);
      if (!task) {
        throw new Error(`Task ${taskId} not found`);
      }

      const dependencies = task.dependencies || [];
      dependencies.push({
        taskId: dependsOnTaskId,
        type
      });

      await task.$query().patch({
        dependencies,
        updatedAt: new Date()
      });

      // 验证是否存在循环依赖
      const hasCycle = await this.detectCircularDependency(taskId);
      if (hasCycle) {
        // 回滚更改
        await task.$query().patch({
          dependencies: task.dependencies,
          updatedAt: new Date()
        });
        throw new Error('Circular dependency detected');
      }

      logger.info('Task dependency added', { taskId, dependsOnTaskId, type });
    } catch (error) {
      logger.error('Failed to add task dependency', { error, taskId, dependsOnTaskId });
      throw error;
    }
  }

  // 获取项目进度概览
  async getProjectProgress(projectId: string): Promise<any> {
    try {
      const ganttChart = await this.generateGanttChart(projectId);
      
      const totalTasks = ganttChart.tasks.length;
      const completedTasks = ganttChart.tasks.filter(t => t.progress === 100).length;
      const inProgressTasks = ganttChart.tasks.filter(t => t.progress > 0 && t.progress < 100).length;
      const notStartedTasks = ganttChart.tasks.filter(t => t.progress === 0).length;
      
      const overallProgress = totalTasks > 0 
        ? ganttChart.tasks.reduce((sum, task) => sum + task.progress, 0) / totalTasks
        : 0;

      const today = new Date();
      const overdueTasks = ganttChart.tasks.filter(t => 
        t.progress < 100 && t.endDate < today
      ).length;

      return {
        totalTasks,
        completedTasks,
        inProgressTasks,
        notStartedTasks,
        overdueTasks,
        overallProgress: Math.round(overallProgress),
        timeline: ganttChart.timeline,
        criticalPath: ganttChart.criticalPath,
        lastUpdated: new Date()
      };
    } catch (error) {
      logger.error('Failed to get project progress', { error, projectId });
      throw error;
    }
  }

  // 私有方法
  private async convertToGanttTasks(project: Project, tasks: Task[], options: GanttOptions): Promise<GanttTask[]> {
    const ganttTasks: GanttTask[] = [];

    // 添加项目阶段（如果存在）
    if (options.groupByPhase && project.phases) {
      project.phases.forEach((phase, index) => {
        ganttTasks.push({
          id: phase.id,
          name: phase.name,
          startDate: phase.startDate,
          endDate: phase.endDate,
          duration: this.calculateDuration(phase.startDate, phase.endDate),
          progress: this.calculatePhaseProgress(phase, tasks),
          dependencies: [],
          priority: 'medium',
          status: phase.status,
          level: 0,
          type: 'phase'
        });
      });
    }

    // 添加里程碑
    if (project.milestones) {
      project.milestones.forEach(milestone => {
        ganttTasks.push({
          id: milestone.id,
          name: milestone.name,
          startDate: milestone.dueDate,
          endDate: milestone.dueDate,
          duration: 0,
          progress: milestone.status === 'completed' ? 100 : 0,
          dependencies: milestone.dependencies || [],
          priority: 'high',
          status: milestone.status,
          level: 1,
          type: 'milestone'
        });
      });
    }

    // 添加任务
    tasks.forEach(task => {
      const parentPhase = this.findTaskPhase(task, project.phases || []);
      
      ganttTasks.push({
        id: task.id,
        name: task.title,
        startDate: task.startDate,
        endDate: task.dueDate,
        duration: this.calculateDuration(task.startDate, task.dueDate),
        progress: task.progress || 0,
        dependencies: this.extractTaskDependencies(task),
        assignee: task.assignedTo,
        priority: task.priority,
        status: task.status,
        parent: parentPhase?.id,
        level: parentPhase ? 2 : 1,
        type: 'task'
      });
    });

    // 排序任务
    return this.sortGanttTasks(ganttTasks);
  }

  private calculateTimeline(tasks: GanttTask[]): { start: Date; end: Date; duration: number } {
    if (tasks.length === 0) {
      const now = new Date();
      return {
        start: now,
        end: now,
        duration: 0
      };
    }

    const startDates = tasks.map(t => t.startDate);
    const endDates = tasks.map(t => t.endDate);

    const start = new Date(Math.min(...startDates.map(d => d.getTime())));
    const end = new Date(Math.max(...endDates.map(d => d.getTime())));
    const duration = this.calculateDuration(start, end);

    return { start, end, duration };
  }

  private calculateCriticalPath(tasks: GanttTask[]): string[] {
    // 简化的关键路径算法
    // 实际实现需要更复杂的网络分析
    const criticalTasks = tasks
      .filter(task => task.type === 'task')
      .sort((a, b) => a.startDate.getTime() - b.startDate.getTime());

    const criticalPath: string[] = [];
    let currentEndDate = new Date(0);

    criticalTasks.forEach(task => {
      if (task.startDate >= currentEndDate) {
        criticalPath.push(task.id);
        currentEndDate = task.endDate;
      }
    });

    return criticalPath;
  }

  private renderGanttSVG(ganttChart: GanttChart, options: GanttOptions): string {
    const width = 1200;
    const height = Math.max(400, ganttChart.tasks.length * 30 + 100);
    const margin = { top: 50, right: 50, bottom: 50, left: 200 };

    // 创建SVG字符串
    let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
    
    // 添加样式
    svg += `<style>
      .task-bar { fill: #4CAF50; stroke: #2E7D32; stroke-width: 1; }
      .task-bar.in-progress { fill: #FF9800; }
      .task-bar.completed { fill: #4CAF50; }
      .task-bar.overdue { fill: #F44336; }
      .milestone { fill: #9C27B0; }
      .phase-bar { fill: #2196F3; opacity: 0.7; }
      .task-text { font-family: Arial, sans-serif; font-size: 12px; fill: #333; }
      .axis-text { font-family: Arial, sans-serif; font-size: 10px; fill: #666; }
      .grid-line { stroke: #E0E0E0; stroke-width: 1; }
      .critical-path { stroke: #F44336; stroke-width: 3; fill: none; }
    </style>`;

    // 计算时间比例
    const timeScale = d3.scaleTime()
      .domain([ganttChart.timeline.start, ganttChart.timeline.end])
      .range([margin.left, width - margin.right]);

    // 绘制时间轴
    svg += this.renderTimeAxis(timeScale, margin, width, height);

    // 绘制网格线
    svg += this.renderGrid(timeScale, ganttChart.tasks.length, margin, width, height);

    // 绘制任务条
    ganttChart.tasks.forEach((task, index) => {
      const y = margin.top + index * 25;
      const x = timeScale(task.startDate);
      const taskWidth = timeScale(task.endDate) - x;

      // 任务条
      const barClass = this.getTaskBarClass(task);
      svg += `<rect class="${barClass}" x="${x}" y="${y}" width="${taskWidth}" height="20" />`;

      // 进度条
      if (task.progress > 0) {
        const progressWidth = taskWidth * (task.progress / 100);
        svg += `<rect fill="#4CAF50" x="${x}" y="${y}" width="${progressWidth}" height="20" opacity="0.8" />`;
      }

      // 任务名称
      svg += `<text class="task-text" x="${margin.left - 10}" y="${y + 15}" text-anchor="end">${task.name}</text>`;

      // 进度百分比
      if (taskWidth > 50) {
        svg += `<text class="task-text" x="${x + taskWidth/2}" y="${y + 15}" text-anchor="middle" fill="white">${task.progress}%</text>`;
      }
    });

    // 绘制关键路径
    if (options.showCriticalPath && ganttChart.criticalPath.length > 0) {
      svg += this.renderCriticalPath(ganttChart, timeScale, margin);
    }

    // 绘制依赖关系
    if (options.showDependencies) {
      svg += this.renderDependencies(ganttChart, timeScale, margin);
    }

    svg += '</svg>';
    return svg;
  }

  private renderTimeAxis(timeScale: any, margin: any, width: number, height: number): string {
    let axis = '';
    
    // 主要时间刻度
    const ticks = timeScale.ticks(10);
    ticks.forEach(tick => {
      const x = timeScale(tick);
      axis += `<line class="grid-line" x1="${x}" y1="${margin.top}" x2="${x}" y2="${height - margin.bottom}" />`;
      axis += `<text class="axis-text" x="${x}" y="${margin.top - 10}" text-anchor="middle">${tick.toLocaleDateString()}</text>`;
    });

    return axis;
  }

  private renderGrid(timeScale: any, taskCount: number, margin: any, width: number, height: number): string {
    let grid = '';
    
    // 水平网格线
    for (let i = 0; i <= taskCount; i++) {
      const y = margin.top + i * 25;
      grid += `<line class="grid-line" x1="${margin.left}" y1="${y}" x2="${width - margin.right}" y2="${y}" />`;
    }

    return grid;
  }

  private renderCriticalPath(ganttChart: GanttChart, timeScale: any, margin: any): string {
    let path = '';
    const criticalTasks = ganttChart.tasks.filter(t => ganttChart.criticalPath.includes(t.id));
    
    if (criticalTasks.length > 1) {
      let pathData = '';
      criticalTasks.forEach((task, index) => {
        const taskIndex = ganttChart.tasks.findIndex(t => t.id === task.id);
        const x = timeScale(task.startDate);
        const y = margin.top + taskIndex * 25 + 10;
        
        if (index === 0) {
          pathData += `M ${x} ${y}`;
        } else {
          pathData += ` L ${x} ${y}`;
        }
      });
      
      path += `<path class="critical-path" d="${pathData}" />`;
    }

    return path;
  }

  private renderDependencies(ganttChart: GanttChart, timeScale: any, margin: any): string {
    let dependencies = '';
    
    ganttChart.tasks.forEach((task, taskIndex) => {
      task.dependencies.forEach(depId => {
        const depTask = ganttChart.tasks.find(t => t.id === depId);
        if (depTask) {
          const depIndex = ganttChart.tasks.findIndex(t => t.id === depId);
          
          const x1 = timeScale(depTask.endDate);
          const y1 = margin.top + depIndex * 25 + 10;
          const x2 = timeScale(task.startDate);
          const y2 = margin.top + taskIndex * 25 + 10;
          
          dependencies += `<line stroke="#666" stroke-width="2" marker-end="url(#arrowhead)" x1="${x1}" y1="${y1}" x2="${x2}" y2="${y2}" />`;
        }
      });
    });

    // 添加箭头标记
    dependencies = `<defs><marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto"><polygon points="0 0, 10 3.5, 0 7" fill="#666" /></marker></defs>` + dependencies;

    return dependencies;
  }

  private getTaskBarClass(task: GanttTask): string {
    if (task.type === 'milestone') return 'milestone';
    if (task.type === 'phase') return 'phase-bar';
    
    if (task.progress === 100) return 'task-bar completed';
    if (task.progress > 0) return 'task-bar in-progress';
    if (new Date() > task.endDate) return 'task-bar overdue';
    
    return 'task-bar';
  }

  private calculateDuration(startDate: Date, endDate: Date): number {
    return Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  private calculatePhaseProgress(phase: any, tasks: Task[]): number {
    const phaseTasks = tasks.filter(task => 
      task.startDate >= phase.startDate && task.dueDate <= phase.endDate
    );
    
    if (phaseTasks.length === 0) return 0;
    
    const totalProgress = phaseTasks.reduce((sum, task) => sum + (task.progress || 0), 0);
    return Math.round(totalProgress / phaseTasks.length);
  }

  private findTaskPhase(task: Task, phases: any[]): any | null {
    return phases.find(phase => 
      task.startDate >= phase.startDate && task.dueDate <= phase.endDate
    ) || null;
  }

  private extractTaskDependencies(task: Task): string[] {
    return task.dependencies?.map((dep: any) => dep.taskId) || [];
  }

  private sortGanttTasks(tasks: GanttTask[]): GanttTask[] {
    return tasks.sort((a, b) => {
      // 先按层级排序
      if (a.level !== b.level) {
        return a.level - b.level;
      }
      // 再按开始时间排序
      return a.startDate.getTime() - b.startDate.getTime();
    });
  }

  private async updateDependentTasks(task: Task): Promise<void> {
    // 查找依赖于此任务的其他任务
    const dependentTasks = await Task.query()
      .where('project_id', task.projectId)
      .whereRaw('dependencies @> ?', [JSON.stringify([{ taskId: task.id }])]);

    // 更新依赖任务的开始时间
    for (const depTask of dependentTasks) {
      const newStartDate = new Date(task.dueDate);
      newStartDate.setDate(newStartDate.getDate() + 1);

      if (newStartDate > depTask.startDate) {
        const duration = this.calculateDuration(depTask.startDate, depTask.dueDate);
        const newEndDate = new Date(newStartDate);
        newEndDate.setDate(newEndDate.getDate() + duration);

        await depTask.$query().patch({
          startDate: newStartDate,
          dueDate: newEndDate,
          updatedAt: new Date()
        });

        // 递归更新依赖任务
        await this.updateDependentTasks(depTask);
      }
    }
  }

  private async detectCircularDependency(taskId: string, visited: Set<string> = new Set()): Promise<boolean> {
    if (visited.has(taskId)) {
      return true; // 发现循环
    }

    visited.add(taskId);

    const task = await Task.query().findById(taskId);
    if (!task || !task.dependencies) {
      return false;
    }

    for (const dep of task.dependencies) {
      if (await this.detectCircularDependency(dep.taskId, new Set(visited))) {
        return true;
      }
    }

    return false;
  }

  private async convertSVGToPNG(svg: string): Promise<Buffer> {
    // 这里应该使用适当的库将SVG转换为PNG
    // 例如使用puppeteer或sharp
    return Buffer.from(svg, 'utf-8');
  }
}
