import { Project } from '../models/Project';
import { Task } from '../models/Task';
import { ProjectPlanningService } from './ProjectPlanningService';
import { ResourceManagementService } from './ResourceManagementService';
import { RiskManagementService } from './RiskManagementService';
import { ProjectAnalyticsService } from './ProjectAnalyticsService';
import { NotificationService } from './NotificationService';
import { EventBus } from '../utils/event-bus';
import { logger } from '../utils/logger';
import { 
  Project as IProject, 
  ProjectStatus, 
  Priority,
  QueryParams,
  PaginatedResult 
} from '@linkagent/shared/types';
import { createError } from '../middleware/error-handler';

export class ProjectService {
  private planningService: ProjectPlanningService;
  private resourceService: ResourceManagementService;
  private riskService: RiskManagementService;
  private analyticsService: ProjectAnalyticsService;
  private notificationService: NotificationService;
  private eventBus: EventBus;

  constructor() {
    this.planningService = new ProjectPlanningService();
    this.resourceService = new ResourceManagementService();
    this.riskService = new RiskManagementService();
    this.analyticsService = new ProjectAnalyticsService();
    this.notificationService = new NotificationService();
    this.eventBus = EventBus.getInstance();
  }

  // 创建项目
  async create(projectData: Partial<IProject>, userId?: string): Promise<Project> {
    try {
      logger.info('Creating new project', { projectData, userId });

      // 数据验证和清洗
      const cleanedData = await this.validateAndCleanProjectData(projectData);

      // 设置默认值
      const defaultData = {
        status: 'planning' as ProjectStatus,
        priority: 'medium' as Priority,
        teamMembers: [],
        tags: [],
        metadata: {},
        methodology: 'agile',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // 创建项目
      const project = await Project.query().insert({
        ...defaultData,
        ...cleanedData
      });

      // 初始化项目计划
      await this.planningService.initializeProject(project);

      // 分配初始资源
      if (cleanedData.teamMembers && cleanedData.teamMembers.length > 0) {
        await this.resourceService.allocateTeamMembers(project, cleanedData.teamMembers);
      }

      // 初始化风险管理
      await this.riskService.initializeRiskManagement(project);

      // 发送事件
      await this.eventBus.publish({
        type: 'project.created',
        aggregateId: project.id,
        data: { project, createdBy: userId },
        timestamp: new Date()
      });

      // 发送通知
      if (project.managerId) {
        await this.notificationService.notifyNewProject(project.managerId, project);
      }

      logger.info('Project created successfully', { projectId: project.id });
      return project;
    } catch (error) {
      logger.error('Failed to create project', { error, projectData });
      throw error;
    }
  }

  // 从合同创建项目
  async createFromContract(contract: any, projectData: Partial<IProject>, userId?: string): Promise<Project> {
    try {
      logger.info('Creating project from contract', { contractId: contract.id, projectData, userId });

      // 从合同数据映射到项目数据
      const mappedData = {
        contractId: contract.id,
        customerId: contract.customerId,
        name: projectData.name || `Project for ${contract.title}`,
        description: projectData.description || contract.description,
        budget: projectData.budget || contract.amount,
        currency: projectData.currency || contract.currency,
        startDate: projectData.startDate || contract.startDate,
        endDate: projectData.endDate || contract.endDate,
        managerId: projectData.managerId || contract.assignedTo,
        metadata: {
          ...projectData.metadata,
          createdFromContract: {
            contractId: contract.id,
            contractTitle: contract.title,
            createdAt: new Date(),
            createdBy: userId
          }
        },
        // 从合同条款生成项目阶段和交付物
        phases: this.generatePhasesFromContract(contract),
        deliverables: this.generateDeliverablesFromContract(contract)
      };

      const project = await this.create(mappedData, userId);

      logger.info('Project created from contract successfully', { 
        projectId: project.id, 
        contractId: contract.id 
      });

      return project;
    } catch (error) {
      logger.error('Failed to create project from contract', { error, contractId: contract.id });
      throw error;
    }
  }

  // 获取项目列表
  async findAll(params: QueryParams): Promise<PaginatedResult<Project>> {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'created_at',
        sortOrder = 'desc',
        search,
        filters = {}
      } = params;

      let query = Project.query()
        .withGraphFetched('[customer, manager, contract]');

      // 搜索
      if (search) {
        query = query.where(builder => {
          builder
            .where('name', 'ilike', `%${search}%`)
            .orWhere('description', 'ilike', `%${search}%`);
        });
      }

      // 过滤器
      if (filters.status) {
        query = query.where('status', filters.status);
      }
      if (filters.priority) {
        query = query.where('priority', filters.priority);
      }
      if (filters.managerId) {
        query = query.where('manager_id', filters.managerId);
      }
      if (filters.customerId) {
        query = query.where('customer_id', filters.customerId);
      }
      if (filters.teamMember) {
        query = query.where('team_members', '@>', JSON.stringify([filters.teamMember]));
      }
      if (filters.budgetMin) {
        query = query.where('budget', '>=', filters.budgetMin);
      }
      if (filters.budgetMax) {
        query = query.where('budget', '<=', filters.budgetMax);
      }
      if (filters.startDateAfter) {
        query = query.where('start_date', '>=', filters.startDateAfter);
      }
      if (filters.endDateBefore) {
        query = query.where('end_date', '<=', filters.endDateBefore);
      }
      if (filters.isOverdue) {
        query = query.where('end_date', '<', new Date())
          .where('status', 'not in', ['completed', 'cancelled']);
      }

      // 排序
      query = query.orderBy(sortBy, sortOrder);

      // 分页
      const result = await query.page(page - 1, limit);

      return {
        data: result.results,
        total: result.total,
        page,
        limit,
        totalPages: Math.ceil(result.total / limit),
        hasNext: page < Math.ceil(result.total / limit),
        hasPrev: page > 1
      };
    } catch (error) {
      logger.error('Failed to fetch projects', { error, params });
      throw error;
    }
  }

  // 获取单个项目
  async findById(id: string): Promise<Project> {
    try {
      const project = await Project.query()
        .findById(id)
        .withGraphFetched('[customer, manager, contract, tasks, timeEntries, documents]');

      if (!project) {
        throw createError.notFound('Project', id);
      }

      return project;
    } catch (error) {
      logger.error('Failed to fetch project', { error, id });
      throw error;
    }
  }

  // 更新项目
  async update(id: string, updateData: Partial<IProject>, userId?: string): Promise<Project> {
    try {
      logger.info('Updating project', { id, updateData, userId });

      const project = await this.findById(id);
      const oldData = { ...project };

      // 数据验证和清洗
      const cleanedData = await this.validateAndCleanProjectData(updateData);

      // 更新项目
      const updatedProject = await project.$query().patchAndFetch({
        ...cleanedData,
        updatedAt: new Date()
      });

      // 记录活动
      await updatedProject.addActivity('updated', 'Project information updated', {
        changes: this.getChanges(oldData, cleanedData),
        updatedBy: userId
      });

      // 如果关键字段发生变化，触发相应的处理
      if (this.shouldUpdatePlan(oldData, cleanedData)) {
        await this.planningService.updateProjectPlan(updatedProject);
      }

      if (this.shouldReallocateResources(oldData, cleanedData)) {
        await this.resourceService.reallocateResources(updatedProject);
      }

      // 发送事件
      await this.eventBus.publish({
        type: 'project.updated',
        aggregateId: id,
        data: { 
          project: updatedProject, 
          oldData, 
          changes: this.getChanges(oldData, cleanedData),
          updatedBy: userId 
        },
        timestamp: new Date()
      });

      logger.info('Project updated successfully', { projectId: id });
      return updatedProject;
    } catch (error) {
      logger.error('Failed to update project', { error, id, updateData });
      throw error;
    }
  }

  // 启动项目
  async start(id: string, userId?: string): Promise<Project> {
    try {
      const project = await this.findById(id);

      if (project.status !== 'planning') {
        throw createError.business('Only planning projects can be started', 'INVALID_PROJECT_STATUS');
      }

      // 验证项目启动条件
      await this.validateProjectStartConditions(project);

      // 启动项目
      await project.start(userId || 'system');

      // 启动相关任务
      await this.planningService.startProjectTasks(project);

      // 分配资源
      await this.resourceService.activateResourceAllocations(project);

      // 发送通知
      await this.notificationService.notifyProjectStarted(project);

      // 发送事件
      await this.eventBus.publish({
        type: 'project.started',
        aggregateId: id,
        data: { project, startedBy: userId },
        timestamp: new Date()
      });

      logger.info('Project started', { projectId: id });
      return await this.findById(id);
    } catch (error) {
      logger.error('Failed to start project', { error, id });
      throw error;
    }
  }

  // 完成项目
  async complete(id: string, userId?: string): Promise<Project> {
    try {
      const project = await this.findById(id);

      if (project.status !== 'in_progress') {
        throw createError.business('Only active projects can be completed', 'INVALID_PROJECT_STATUS');
      }

      // 验证项目完成条件
      await this.validateProjectCompletionConditions(project);

      // 完成项目
      await project.complete(userId || 'system');

      // 完成相关任务
      await this.planningService.completeProjectTasks(project);

      // 释放资源
      await this.resourceService.releaseResources(project);

      // 生成项目总结报告
      const summary = await this.analyticsService.generateProjectSummary(project);

      // 发送通知
      await this.notificationService.notifyProjectCompleted(project, summary);

      // 发送事件
      await this.eventBus.publish({
        type: 'project.completed',
        aggregateId: id,
        data: { project, summary, completedBy: userId },
        timestamp: new Date()
      });

      logger.info('Project completed', { projectId: id });
      return await this.findById(id);
    } catch (error) {
      logger.error('Failed to complete project', { error, id });
      throw error;
    }
  }

  // 暂停项目
  async pause(id: string, reason: string, userId?: string): Promise<Project> {
    try {
      const project = await this.findById(id);
      await project.pause(reason, userId || 'system');

      // 暂停相关任务
      await this.planningService.pauseProjectTasks(project);

      // 暂停资源分配
      await this.resourceService.pauseResourceAllocations(project);

      // 发送通知
      await this.notificationService.notifyProjectPaused(project, reason);

      // 发送事件
      await this.eventBus.publish({
        type: 'project.paused',
        aggregateId: id,
        data: { project, reason, pausedBy: userId },
        timestamp: new Date()
      });

      logger.info('Project paused', { projectId: id, reason });
      return await this.findById(id);
    } catch (error) {
      logger.error('Failed to pause project', { error, id });
      throw error;
    }
  }

  // 取消项目
  async cancel(id: string, reason: string, userId?: string): Promise<Project> {
    try {
      const project = await this.findById(id);
      await project.cancel(reason, userId || 'system');

      // 取消相关任务
      await this.planningService.cancelProjectTasks(project);

      // 释放资源
      await this.resourceService.releaseResources(project);

      // 发送通知
      await this.notificationService.notifyProjectCancelled(project, reason);

      // 发送事件
      await this.eventBus.publish({
        type: 'project.cancelled',
        aggregateId: id,
        data: { project, reason, cancelledBy: userId },
        timestamp: new Date()
      });

      logger.info('Project cancelled', { projectId: id, reason });
      return await this.findById(id);
    } catch (error) {
      logger.error('Failed to cancel project', { error, id });
      throw error;
    }
  }

  // 添加团队成员
  async addTeamMember(id: string, userId: string, role?: string, managerId?: string): Promise<Project> {
    try {
      const project = await this.findById(id);
      await project.addTeamMember(userId, role);

      // 分配资源
      await this.resourceService.allocateTeamMember(project, userId, role);

      // 发送通知
      await this.notificationService.notifyTeamMemberAdded(userId, project, role);

      // 发送事件
      await this.eventBus.publish({
        type: 'project.team_member_added',
        aggregateId: id,
        data: { project, userId, role, addedBy: managerId },
        timestamp: new Date()
      });

      logger.info('Team member added to project', { projectId: id, userId, role });
      return await this.findById(id);
    } catch (error) {
      logger.error('Failed to add team member', { error, id, userId });
      throw error;
    }
  }

  // 移除团队成员
  async removeTeamMember(id: string, userId: string, managerId?: string): Promise<Project> {
    try {
      const project = await this.findById(id);
      await project.removeTeamMember(userId);

      // 重新分配该成员的任务
      await this.planningService.reassignUserTasks(project, userId);

      // 释放资源分配
      await this.resourceService.deallocateTeamMember(project, userId);

      // 发送通知
      await this.notificationService.notifyTeamMemberRemoved(userId, project);

      // 发送事件
      await this.eventBus.publish({
        type: 'project.team_member_removed',
        aggregateId: id,
        data: { project, userId, removedBy: managerId },
        timestamp: new Date()
      });

      logger.info('Team member removed from project', { projectId: id, userId });
      return await this.findById(id);
    } catch (error) {
      logger.error('Failed to remove team member', { error, id, userId });
      throw error;
    }
  }

  // 获取项目仪表板数据
  async getDashboard(id: string): Promise<any> {
    try {
      const project = await this.findById(id);
      
      // 获取项目统计数据
      const stats = await this.analyticsService.getProjectStatistics(project);
      
      // 获取任务统计
      const taskStats = await this.getTaskStatistics(id);
      
      // 获取风险和问题统计
      const riskStats = await this.riskService.getRiskStatistics(project);
      
      // 获取资源利用率
      const resourceStats = await this.resourceService.getResourceUtilization(project);
      
      // 获取进度趋势
      const progressTrend = await this.analyticsService.getProgressTrend(project);

      return {
        project,
        statistics: stats,
        tasks: taskStats,
        risks: riskStats,
        resources: resourceStats,
        progressTrend
      };
    } catch (error) {
      logger.error('Failed to get project dashboard', { error, id });
      throw error;
    }
  }

  // 私有方法
  private async validateAndCleanProjectData(data: Partial<IProject>): Promise<Partial<IProject>> {
    const cleaned = { ...data };

    // 验证预算
    if (cleaned.budget !== undefined && cleaned.budget < 0) {
      throw createError.validation('Budget must be non-negative');
    }

    // 验证日期
    if (cleaned.startDate && cleaned.endDate && cleaned.startDate >= cleaned.endDate) {
      throw createError.validation('End date must be after start date');
    }

    // 清理标签
    if (cleaned.tags) {
      cleaned.tags = cleaned.tags.map(tag => tag.trim().toLowerCase()).filter(Boolean);
    }

    return cleaned;
  }

  private generatePhasesFromContract(contract: any): any[] {
    // 从合同条款生成项目阶段
    const phases = [];
    
    if (contract.terms) {
      contract.terms.forEach((term: any, index: number) => {
        if (term.type === 'delivery') {
          phases.push({
            id: `phase-${index + 1}`,
            name: term.title,
            description: term.content,
            startDate: new Date(),
            endDate: new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)), // 30天后
            status: 'not_started',
            deliverables: [],
            milestones: []
          });
        }
      });
    }

    return phases;
  }

  private generateDeliverablesFromContract(contract: any): any[] {
    // 从合同条款生成交付物
    const deliverables = [];
    
    if (contract.terms) {
      contract.terms.forEach((term: any, index: number) => {
        if (term.type === 'delivery') {
          deliverables.push({
            id: `deliverable-${index + 1}`,
            name: term.title,
            description: term.content,
            type: 'document',
            dueDate: new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)),
            status: 'pending'
          });
        }
      });
    }

    return deliverables;
  }

  private async validateProjectStartConditions(project: Project): Promise<void> {
    // 验证项目启动条件
    if (!project.managerId) {
      throw createError.validation('Project must have a manager assigned');
    }

    if (project.teamMembers.length === 0) {
      throw createError.validation('Project must have at least one team member');
    }

    // 验证是否有必要的资源
    const resourceCheck = await this.resourceService.validateResourceAvailability(project);
    if (!resourceCheck.available) {
      throw createError.business('Required resources are not available', 'INSUFFICIENT_RESOURCES');
    }
  }

  private async validateProjectCompletionConditions(project: Project): Promise<void> {
    // 验证项目完成条件
    const incompleteTasks = await Task.query()
      .where('project_id', project.id)
      .where('status', 'not in', ['completed', 'cancelled'])
      .resultSize();

    if (incompleteTasks > 0) {
      throw createError.business('Cannot complete project with incomplete tasks', 'INCOMPLETE_TASKS_EXIST');
    }

    // 验证所有交付物是否已完成
    const incompleteDeliverables = project.deliverables?.filter(d => d.status !== 'approved').length || 0;
    if (incompleteDeliverables > 0) {
      throw createError.business('Cannot complete project with incomplete deliverables', 'INCOMPLETE_DELIVERABLES_EXIST');
    }
  }

  private async getTaskStatistics(projectId: string): Promise<any> {
    const tasks = await Task.query().where('project_id', projectId);
    
    const stats = {
      total: tasks.length,
      completed: tasks.filter(t => t.status === 'completed').length,
      inProgress: tasks.filter(t => t.status === 'in_progress').length,
      pending: tasks.filter(t => t.status === 'pending').length,
      overdue: tasks.filter(t => t.status !== 'completed' && new Date() > t.dueDate).length
    };

    return {
      ...stats,
      completionRate: stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0
    };
  }

  private shouldUpdatePlan(oldData: any, newData: any): boolean {
    const planFields = ['startDate', 'endDate', 'phases', 'milestones'];
    return planFields.some(field => 
      JSON.stringify(oldData[field]) !== JSON.stringify(newData[field])
    );
  }

  private shouldReallocateResources(oldData: any, newData: any): boolean {
    const resourceFields = ['teamMembers', 'budget', 'resources'];
    return resourceFields.some(field => 
      JSON.stringify(oldData[field]) !== JSON.stringify(newData[field])
    );
  }

  private getChanges(oldData: any, newData: any): Record<string, { old: any; new: any }> {
    const changes: Record<string, { old: any; new: any }> = {};
    
    Object.keys(newData).forEach(key => {
      if (JSON.stringify(oldData[key]) !== JSON.stringify(newData[key])) {
        changes[key] = {
          old: oldData[key],
          new: newData[key]
        };
      }
    });
    
    return changes;
  }
}
