import { Model } from 'objection';
import { BaseModel } from '@linkagent/shared/models/BaseModel';
import { Project as IProject, ProjectStatus, Priority } from '@linkagent/shared/types';

export class Project extends BaseModel implements IProject {
  static tableName = 'projects';

  // 基础字段
  id!: string;
  customerId!: string;
  contractId?: string;
  name!: string;
  description?: string;
  status!: ProjectStatus;
  priority!: Priority;
  startDate!: Date;
  endDate!: Date;
  actualStartDate?: Date;
  actualEndDate?: Date;
  budget!: number;
  currency!: string;
  actualCost?: number;
  managerId!: string;
  teamMembers!: string[];
  tags!: string[];
  metadata!: Record<string, any>;
  createdAt!: Date;
  updatedAt!: Date;

  // 扩展字段
  projectType?: string; // 项目类型
  methodology?: 'waterfall' | 'agile' | 'scrum' | 'kanban' | 'hybrid'; // 项目方法论
  phase?: string; // 当前阶段
  phases?: Array<{
    id: string;
    name: string;
    description?: string;
    startDate: Date;
    endDate: Date;
    status: 'not_started' | 'in_progress' | 'completed' | 'on_hold';
    deliverables: string[];
    milestones: string[];
  }>; // 项目阶段
  milestones?: Array<{
    id: string;
    name: string;
    description?: string;
    dueDate: Date;
    status: 'pending' | 'completed' | 'overdue';
    completedDate?: Date;
    dependencies?: string[];
  }>; // 里程碑
  deliverables?: Array<{
    id: string;
    name: string;
    description?: string;
    type: string;
    dueDate: Date;
    status: 'pending' | 'in_progress' | 'completed' | 'approved';
    assignedTo?: string;
    approvedBy?: string;
    approvedDate?: Date;
  }>; // 交付物
  risks?: Array<{
    id: string;
    title: string;
    description: string;
    category: string;
    probability: 'low' | 'medium' | 'high';
    impact: 'low' | 'medium' | 'high';
    severity: 'low' | 'medium' | 'high' | 'critical';
    status: 'identified' | 'analyzing' | 'mitigating' | 'monitoring' | 'closed';
    owner?: string;
    mitigation?: string;
    contingency?: string;
    identifiedDate: Date;
    lastReviewDate?: Date;
  }>; // 风险
  issues?: Array<{
    id: string;
    title: string;
    description: string;
    category: string;
    priority: Priority;
    status: 'open' | 'in_progress' | 'resolved' | 'closed';
    assignedTo?: string;
    reportedBy: string;
    reportedDate: Date;
    resolvedDate?: Date;
    resolution?: string;
  }>; // 问题
  resources?: Array<{
    id: string;
    name: string;
    type: 'human' | 'equipment' | 'material' | 'software' | 'other';
    quantity: number;
    unit: string;
    cost: number;
    allocatedFrom?: Date;
    allocatedTo?: Date;
    utilization?: number; // 利用率百分比
  }>; // 资源
  communications?: Array<{
    id: string;
    type: 'meeting' | 'email' | 'report' | 'presentation' | 'other';
    subject: string;
    content?: string;
    participants: string[];
    date: Date;
    attachments?: string[];
  }>; // 沟通记录
  qualityMetrics?: {
    defectRate?: number;
    customerSatisfaction?: number;
    codeQuality?: number;
    testCoverage?: number;
    performanceScore?: number;
  }; // 质量指标
  progressMetrics?: {
    tasksCompleted: number;
    totalTasks: number;
    hoursSpent: number;
    estimatedHours: number;
    budgetSpent: number;
    scheduleVariance: number; // 进度偏差（天）
    costVariance: number; // 成本偏差
  }; // 进度指标
  stakeholders?: Array<{
    id: string;
    name: string;
    role: string;
    organization?: string;
    contactInfo: Record<string, any>;
    influence: 'low' | 'medium' | 'high';
    interest: 'low' | 'medium' | 'high';
    communicationPreference: string;
  }>; // 利益相关者
  approvals?: Array<{
    id: string;
    type: string;
    description: string;
    requiredBy: Date;
    approver: string;
    status: 'pending' | 'approved' | 'rejected';
    approvedDate?: Date;
    comments?: string;
  }>; // 审批

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['customerId', 'name', 'status', 'priority', 'startDate', 'endDate', 'budget', 'currency', 'managerId'],
      properties: {
        id: { type: 'string', format: 'uuid' },
        customerId: { type: 'string', format: 'uuid' },
        contractId: { type: ['string', 'null'], format: 'uuid' },
        name: { type: 'string', minLength: 1, maxLength: 255 },
        description: { type: ['string', 'null'], maxLength: 2000 },
        status: { 
          type: 'string', 
          enum: ['planning', 'in_progress', 'on_hold', 'completed', 'cancelled'] 
        },
        priority: { 
          type: 'string', 
          enum: ['low', 'medium', 'high', 'urgent'] 
        },
        startDate: { type: 'string', format: 'date' },
        endDate: { type: 'string', format: 'date' },
        actualStartDate: { type: ['string', 'null'], format: 'date' },
        actualEndDate: { type: ['string', 'null'], format: 'date' },
        budget: { type: 'number', minimum: 0 },
        currency: { type: 'string', minLength: 3, maxLength: 3 },
        actualCost: { type: ['number', 'null'], minimum: 0 },
        managerId: { type: 'string', format: 'uuid' },
        teamMembers: { type: 'array', items: { type: 'string', format: 'uuid' } },
        tags: { type: 'array', items: { type: 'string' } },
        metadata: { type: 'object' },
        projectType: { type: ['string', 'null'] },
        methodology: { 
          type: ['string', 'null'], 
          enum: ['waterfall', 'agile', 'scrum', 'kanban', 'hybrid'] 
        },
        phase: { type: ['string', 'null'] },
        phases: { type: ['array', 'null'] },
        milestones: { type: ['array', 'null'] },
        deliverables: { type: ['array', 'null'] },
        risks: { type: ['array', 'null'] },
        issues: { type: ['array', 'null'] },
        resources: { type: ['array', 'null'] },
        communications: { type: ['array', 'null'] },
        qualityMetrics: { type: ['object', 'null'] },
        progressMetrics: { type: ['object', 'null'] },
        stakeholders: { type: ['array', 'null'] },
        approvals: { type: ['array', 'null'] }
      }
    };
  }

  static get relationMappings() {
    return {
      customer: {
        relation: Model.BelongsToOneRelation,
        modelClass: 'Customer',
        join: {
          from: 'projects.customer_id',
          to: 'customers.id'
        }
      },
      contract: {
        relation: Model.BelongsToOneRelation,
        modelClass: 'Contract',
        join: {
          from: 'projects.contract_id',
          to: 'contracts.id'
        }
      },
      manager: {
        relation: Model.BelongsToOneRelation,
        modelClass: 'User',
        join: {
          from: 'projects.manager_id',
          to: 'users.id'
        }
      },
      tasks: {
        relation: Model.HasManyRelation,
        modelClass: 'Task',
        join: {
          from: 'projects.id',
          to: 'tasks.project_id'
        }
      },
      timeEntries: {
        relation: Model.HasManyRelation,
        modelClass: 'TimeEntry',
        join: {
          from: 'projects.id',
          to: 'time_entries.project_id'
        }
      },
      documents: {
        relation: Model.HasManyRelation,
        modelClass: 'ProjectDocument',
        join: {
          from: 'projects.id',
          to: 'project_documents.project_id'
        }
      }
    };
  }

  // 虚拟属性
  get isActive(): boolean {
    return this.status === 'in_progress';
  }

  get isCompleted(): boolean {
    return this.status === 'completed';
  }

  get isOverdue(): boolean {
    return !this.isCompleted && new Date() > this.endDate;
  }

  get daysRemaining(): number {
    if (this.isCompleted) return 0;
    return Math.ceil((this.endDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
  }

  get duration(): number {
    return Math.ceil((this.endDate.getTime() - this.startDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  get actualDuration(): number {
    if (!this.actualStartDate) return 0;
    const endDate = this.actualEndDate || new Date();
    return Math.ceil((endDate.getTime() - this.actualStartDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  get progressPercentage(): number {
    if (!this.progressMetrics) return 0;
    const { tasksCompleted, totalTasks } = this.progressMetrics;
    return totalTasks > 0 ? Math.round((tasksCompleted / totalTasks) * 100) : 0;
  }

  get budgetUtilization(): number {
    if (!this.actualCost) return 0;
    return Math.round((this.actualCost / this.budget) * 100);
  }

  get schedulePerformanceIndex(): number {
    if (!this.progressMetrics) return 1;
    const { scheduleVariance } = this.progressMetrics;
    const plannedDuration = this.duration;
    return plannedDuration > 0 ? (plannedDuration - scheduleVariance) / plannedDuration : 1;
  }

  get costPerformanceIndex(): number {
    if (!this.progressMetrics || !this.actualCost) return 1;
    const { budgetSpent } = this.progressMetrics;
    return budgetSpent > 0 ? this.budget / budgetSpent : 1;
  }

  get healthScore(): number {
    const spi = this.schedulePerformanceIndex;
    const cpi = this.costPerformanceIndex;
    const progress = this.progressPercentage / 100;
    
    // 综合健康评分
    const score = (spi * 0.4 + cpi * 0.4 + progress * 0.2) * 100;
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  get riskLevel(): 'low' | 'medium' | 'high' | 'critical' {
    if (!this.risks || this.risks.length === 0) return 'low';
    
    const criticalRisks = this.risks.filter(r => r.severity === 'critical' && r.status !== 'closed').length;
    const highRisks = this.risks.filter(r => r.severity === 'high' && r.status !== 'closed').length;
    
    if (criticalRisks > 0) return 'critical';
    if (highRisks > 2) return 'high';
    if (highRisks > 0 || this.risks.filter(r => r.severity === 'medium' && r.status !== 'closed').length > 3) return 'medium';
    return 'low';
  }

  // 实例方法
  async start(userId: string): Promise<void> {
    this.status = 'in_progress';
    this.actualStartDate = new Date();
    
    await this.$query().patch({
      status: this.status,
      actualStartDate: this.actualStartDate,
      updatedAt: new Date()
    });

    await this.addActivity('started', 'Project started', { startedBy: userId });
  }

  async complete(userId: string): Promise<void> {
    this.status = 'completed';
    this.actualEndDate = new Date();
    
    await this.$query().patch({
      status: this.status,
      actualEndDate: this.actualEndDate,
      updatedAt: new Date()
    });

    await this.addActivity('completed', 'Project completed', { completedBy: userId });
  }

  async pause(reason: string, userId: string): Promise<void> {
    this.status = 'on_hold';
    
    await this.$query().patch({
      status: this.status,
      updatedAt: new Date()
    });

    await this.addActivity('paused', `Project paused: ${reason}`, { pausedBy: userId, reason });
  }

  async cancel(reason: string, userId: string): Promise<void> {
    this.status = 'cancelled';
    
    await this.$query().patch({
      status: this.status,
      updatedAt: new Date()
    });

    await this.addActivity('cancelled', `Project cancelled: ${reason}`, { cancelledBy: userId, reason });
  }

  async addTeamMember(userId: string, role?: string): Promise<void> {
    if (!this.teamMembers.includes(userId)) {
      this.teamMembers.push(userId);
      
      await this.$query().patch({
        teamMembers: this.teamMembers,
        updatedAt: new Date()
      });

      await this.addActivity('team_member_added', `Team member added: ${userId}`, { userId, role });
    }
  }

  async removeTeamMember(userId: string): Promise<void> {
    const index = this.teamMembers.indexOf(userId);
    if (index > -1) {
      this.teamMembers.splice(index, 1);
      
      await this.$query().patch({
        teamMembers: this.teamMembers,
        updatedAt: new Date()
      });

      await this.addActivity('team_member_removed', `Team member removed: ${userId}`, { userId });
    }
  }

  async addMilestone(milestone: any): Promise<void> {
    const milestones = this.milestones || [];
    milestones.push({
      ...milestone,
      id: milestone.id || require('uuid').v4(),
      status: 'pending'
    });

    await this.$query().patch({
      milestones,
      updatedAt: new Date()
    });

    await this.addActivity('milestone_added', `Milestone added: ${milestone.name}`, { milestone });
  }

  async completeMilestone(milestoneId: string, userId: string): Promise<void> {
    const milestones = this.milestones || [];
    const milestone = milestones.find(m => m.id === milestoneId);
    
    if (milestone) {
      milestone.status = 'completed';
      milestone.completedDate = new Date();

      await this.$query().patch({
        milestones,
        updatedAt: new Date()
      });

      await this.addActivity('milestone_completed', `Milestone completed: ${milestone.name}`, { 
        milestoneId, 
        completedBy: userId 
      });
    }
  }

  async addRisk(risk: any, userId: string): Promise<void> {
    const risks = this.risks || [];
    risks.push({
      ...risk,
      id: risk.id || require('uuid').v4(),
      status: 'identified',
      identifiedDate: new Date()
    });

    await this.$query().patch({
      risks,
      updatedAt: new Date()
    });

    await this.addActivity('risk_identified', `Risk identified: ${risk.title}`, { risk, identifiedBy: userId });
  }

  async updateRisk(riskId: string, updates: any, userId: string): Promise<void> {
    const risks = this.risks || [];
    const risk = risks.find(r => r.id === riskId);
    
    if (risk) {
      Object.assign(risk, updates, { lastReviewDate: new Date() });

      await this.$query().patch({
        risks,
        updatedAt: new Date()
      });

      await this.addActivity('risk_updated', `Risk updated: ${risk.title}`, { 
        riskId, 
        updates, 
        updatedBy: userId 
      });
    }
  }

  async addIssue(issue: any, userId: string): Promise<void> {
    const issues = this.issues || [];
    issues.push({
      ...issue,
      id: issue.id || require('uuid').v4(),
      status: 'open',
      reportedBy: userId,
      reportedDate: new Date()
    });

    await this.$query().patch({
      issues,
      updatedAt: new Date()
    });

    await this.addActivity('issue_reported', `Issue reported: ${issue.title}`, { issue, reportedBy: userId });
  }

  async resolveIssue(issueId: string, resolution: string, userId: string): Promise<void> {
    const issues = this.issues || [];
    const issue = issues.find(i => i.id === issueId);
    
    if (issue) {
      issue.status = 'resolved';
      issue.resolution = resolution;
      issue.resolvedDate = new Date();

      await this.$query().patch({
        issues,
        updatedAt: new Date()
      });

      await this.addActivity('issue_resolved', `Issue resolved: ${issue.title}`, { 
        issueId, 
        resolution, 
        resolvedBy: userId 
      });
    }
  }

  async updateProgress(metrics: any): Promise<void> {
    this.progressMetrics = {
      ...this.progressMetrics,
      ...metrics
    };

    await this.$query().patch({
      progressMetrics: this.progressMetrics,
      updatedAt: new Date()
    });
  }

  async addActivity(type: string, description: string, metadata?: Record<string, any>): Promise<void> {
    // 这里应该添加到项目活动表
    console.log('Project activity:', {
      projectId: this.id,
      type,
      description,
      metadata,
      timestamp: new Date()
    });
  }

  // 静态方法
  static async findByManager(managerId: string): Promise<Project[]> {
    return this.query()
      .where('manager_id', managerId)
      .orderBy('created_at', 'desc');
  }

  static async findByTeamMember(userId: string): Promise<Project[]> {
    return this.query()
      .where('team_members', '@>', JSON.stringify([userId]))
      .orderBy('created_at', 'desc');
  }

  static async findByStatus(status: ProjectStatus): Promise<Project[]> {
    return this.query()
      .where('status', status)
      .orderBy('end_date', 'asc');
  }

  static async findOverdue(): Promise<Project[]> {
    return this.query()
      .where('end_date', '<', new Date())
      .where('status', 'not in', ['completed', 'cancelled'])
      .orderBy('end_date', 'asc');
  }

  static async findAtRisk(): Promise<Project[]> {
    // 这里需要复杂的查询逻辑来识别风险项目
    return this.query()
      .where('status', 'in_progress')
      .orderBy('end_date', 'asc');
  }
}
