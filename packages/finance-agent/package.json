{"name": "@linkagent/finance-agent", "version": "1.0.0", "description": "Link Agent财务结算智能体服务", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "nodemon --exec ts-node src/index.ts", "start": "node dist/index.js", "clean": "rm -rf dist", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "docker:build": "docker build -t linkagent/finance-agent .", "docker:run": "docker run -p 3013:3000 linkagent/finance-agent"}, "keywords": ["linkagent", "finance", "agent", "billing", "invoice", "payment", "accounting", "ai"], "author": "Link Agent Team", "license": "MIT", "dependencies": {"@linkagent/shared": "^1.0.0", "express": "^4.18.0", "express-rate-limit": "^7.1.0", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "morgan": "^1.10.0", "express-validator": "^7.0.0", "express-async-errors": "^3.1.1", "jsonwebtoken": "^9.0.0", "bcrypt": "^5.1.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.0", "nodemailer": "^6.9.0", "twilio": "^4.19.0", "ioredis": "^5.3.0", "bull": "^4.12.0", "axios": "^1.6.0", "node-cron": "^3.0.0", "uuid": "^9.0.0", "dayjs": "^1.11.0", "lodash": "^4.17.21", "joi": "^17.11.0", "openai": "^4.20.0", "langchain": "^0.0.200", "@pinecone-database/pinecone": "^1.1.0", "moment": "^2.29.0", "numeral": "^2.0.6", "currency.js": "^2.0.4", "decimal.js": "^10.4.0", "accounting": "^0.4.1", "pdf-lib": "^1.17.0", "puppeteer": "^21.5.0", "handlebars": "^4.7.0", "stripe": "^14.7.0", "paypal-rest-sdk": "^1.8.1", "alipay-sdk": "^3.4.0", "wechatpay-node-v3": "^2.1.0", "quickbooks": "^2.0.0", "xero-node": "^4.34.0", "sage-accounting": "^1.0.0", "bank-statement-parser": "^1.0.0", "ofx-js": "^0.1.0", "csv-parser": "^3.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^20.0.0", "@types/express": "^4.17.0", "@types/cors": "^2.8.0", "@types/compression": "^1.7.0", "@types/morgan": "^1.9.0", "@types/bcrypt": "^5.0.0", "@types/jsonwebtoken": "^9.0.0", "@types/multer": "^1.4.0", "@types/nodemailer": "^6.4.0", "@types/uuid": "^9.0.0", "@types/lodash": "^4.14.0", "@types/node-cron": "^3.0.0", "@types/numeral": "^2.0.0", "@types/accounting": "^0.4.0", "@types/handlebars": "^4.1.0", "@types/paypal-rest-sdk": "^1.7.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "ts-jest": "^29.0.0", "supertest": "^6.3.0", "@types/supertest": "^2.0.0", "nodemon": "^3.0.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}