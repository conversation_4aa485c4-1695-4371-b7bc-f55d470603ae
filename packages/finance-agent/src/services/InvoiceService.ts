import { Invoice } from '../models/Invoice';
import { InvoiceTemplateService } from './InvoiceTemplateService';
import { PaymentProcessingService } from './PaymentProcessingService';
import { TaxCalculationService } from './TaxCalculationService';
import { BillingRuleService } from './BillingRuleService';
import { NotificationService } from './NotificationService';
import { EventBus } from '../utils/event-bus';
import { logger } from '../utils/logger';
import { 
  Invoice as IInvoice, 
  InvoiceStatus, 
  InvoiceType,
  QueryParams,
  PaginatedResult 
} from '@linkagent/shared/types';
import { createError } from '../middleware/error-handler';

export class InvoiceService {
  private templateService: InvoiceTemplateService;
  private paymentService: PaymentProcessingService;
  private taxService: TaxCalculationService;
  private billingService: BillingRuleService;
  private notificationService: NotificationService;
  private eventBus: EventBus;

  constructor() {
    this.templateService = new InvoiceTemplateService();
    this.paymentService = new PaymentProcessingService();
    this.taxService = new TaxCalculationService();
    this.billingService = new BillingRuleService();
    this.notificationService = new NotificationService();
    this.eventBus = EventBus.getInstance();
  }

  // 创建发票
  async create(invoiceData: Partial<IInvoice>, userId?: string): Promise<Invoice> {
    try {
      logger.info('Creating new invoice', { invoiceData, userId });

      // 数据验证和清洗
      const cleanedData = await this.validateAndCleanInvoiceData(invoiceData);

      // 生成发票编号
      const invoiceNumber = await Invoice.generateInvoiceNumber(cleanedData.type || 'standard');

      // 应用计费规则
      const billingRules = await this.billingService.getApplicableRules(cleanedData);
      const processedData = await this.billingService.applyRules(cleanedData, billingRules);

      // 计算税额
      const taxDetails = await this.taxService.calculateTax(processedData);
      processedData.taxDetails = taxDetails;
      processedData.taxAmount = taxDetails.reduce((sum, tax) => sum + tax.taxAmount, 0);

      // 计算总额
      processedData.totalAmount = processedData.subtotal + processedData.taxAmount - (processedData.discountAmount || 0);

      // 设置默认值
      const defaultData = {
        invoiceNumber,
        status: 'draft' as InvoiceStatus,
        type: 'standard' as InvoiceType,
        issueDate: new Date(),
        paidAmount: 0,
        currency: 'CNY',
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // 创建发票
      const invoice = await Invoice.query().insert({
        ...defaultData,
        ...processedData
      });

      // 发送事件
      await this.eventBus.publish({
        type: 'invoice.created',
        aggregateId: invoice.id,
        data: { invoice, createdBy: userId },
        timestamp: new Date()
      });

      logger.info('Invoice created successfully', { invoiceId: invoice.id });
      return invoice;
    } catch (error) {
      logger.error('Failed to create invoice', { error, invoiceData });
      throw error;
    }
  }

  // 从项目创建发票
  async createFromProject(project: any, invoiceData: Partial<IInvoice>, userId?: string): Promise<Invoice> {
    try {
      logger.info('Creating invoice from project', { projectId: project.id, invoiceData, userId });

      // 从项目数据生成发票明细
      const lineItems = await this.generateLineItemsFromProject(project);

      // 计算小计
      const subtotal = lineItems.reduce((sum, item) => sum + item.totalPrice, 0);

      // 从项目数据映射到发票数据
      const mappedData = {
        customerId: project.customerId,
        projectId: project.id,
        contractId: project.contractId,
        description: invoiceData.description || `Invoice for project: ${project.name}`,
        lineItems,
        subtotal,
        currency: invoiceData.currency || project.currency,
        dueDate: invoiceData.dueDate || this.calculateDefaultDueDate(),
        billingAddress: invoiceData.billingAddress || await this.getCustomerBillingAddress(project.customerId),
        metadata: {
          ...invoiceData.metadata,
          createdFromProject: {
            projectId: project.id,
            projectName: project.name,
            createdAt: new Date(),
            createdBy: userId
          }
        }
      };

      const invoice = await this.create({ ...invoiceData, ...mappedData }, userId);

      logger.info('Invoice created from project successfully', { 
        invoiceId: invoice.id, 
        projectId: project.id 
      });

      return invoice;
    } catch (error) {
      logger.error('Failed to create invoice from project', { error, projectId: project.id });
      throw error;
    }
  }

  // 从合同创建发票
  async createFromContract(contract: any, invoiceData: Partial<IInvoice>, userId?: string): Promise<Invoice> {
    try {
      logger.info('Creating invoice from contract', { contractId: contract.id, invoiceData, userId });

      // 从合同条款生成发票明细
      const lineItems = await this.generateLineItemsFromContract(contract);

      // 计算小计
      const subtotal = lineItems.reduce((sum, item) => sum + item.totalPrice, 0);

      // 从合同数据映射到发票数据
      const mappedData = {
        customerId: contract.customerId,
        contractId: contract.id,
        description: invoiceData.description || `Invoice for contract: ${contract.title}`,
        lineItems,
        subtotal,
        currency: invoiceData.currency || contract.currency,
        dueDate: invoiceData.dueDate || this.calculateDefaultDueDate(),
        paymentTerms: contract.paymentTerms,
        metadata: {
          ...invoiceData.metadata,
          createdFromContract: {
            contractId: contract.id,
            contractTitle: contract.title,
            createdAt: new Date(),
            createdBy: userId
          }
        }
      };

      const invoice = await this.create({ ...invoiceData, ...mappedData }, userId);

      logger.info('Invoice created from contract successfully', { 
        invoiceId: invoice.id, 
        contractId: contract.id 
      });

      return invoice;
    } catch (error) {
      logger.error('Failed to create invoice from contract', { error, contractId: contract.id });
      throw error;
    }
  }

  // 获取发票列表
  async findAll(params: QueryParams): Promise<PaginatedResult<Invoice>> {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'issue_date',
        sortOrder = 'desc',
        search,
        filters = {}
      } = params;

      let query = Invoice.query()
        .withGraphFetched('[customer, project, contract]');

      // 搜索
      if (search) {
        query = query.where(builder => {
          builder
            .where('invoice_number', 'ilike', `%${search}%`)
            .orWhere('description', 'ilike', `%${search}%`);
        });
      }

      // 过滤器
      if (filters.status) {
        query = query.where('status', filters.status);
      }
      if (filters.type) {
        query = query.where('type', filters.type);
      }
      if (filters.customerId) {
        query = query.where('customer_id', filters.customerId);
      }
      if (filters.projectId) {
        query = query.where('project_id', filters.projectId);
      }
      if (filters.contractId) {
        query = query.where('contract_id', filters.contractId);
      }
      if (filters.currency) {
        query = query.where('currency', filters.currency);
      }
      if (filters.amountMin) {
        query = query.where('total_amount', '>=', filters.amountMin);
      }
      if (filters.amountMax) {
        query = query.where('total_amount', '<=', filters.amountMax);
      }
      if (filters.issueDateAfter) {
        query = query.where('issue_date', '>=', filters.issueDateAfter);
      }
      if (filters.issueDateBefore) {
        query = query.where('issue_date', '<=', filters.issueDateBefore);
      }
      if (filters.dueDateAfter) {
        query = query.where('due_date', '>=', filters.dueDateAfter);
      }
      if (filters.dueDateBefore) {
        query = query.where('due_date', '<=', filters.dueDateBefore);
      }
      if (filters.isOverdue) {
        query = query.where('due_date', '<', new Date())
          .where('status', 'not in', ['paid', 'cancelled', 'refunded']);
      }

      // 排序
      query = query.orderBy(sortBy, sortOrder);

      // 分页
      const result = await query.page(page - 1, limit);

      return {
        data: result.results,
        total: result.total,
        page,
        limit,
        totalPages: Math.ceil(result.total / limit),
        hasNext: page < Math.ceil(result.total / limit),
        hasPrev: page > 1
      };
    } catch (error) {
      logger.error('Failed to fetch invoices', { error, params });
      throw error;
    }
  }

  // 获取单个发票
  async findById(id: string): Promise<Invoice> {
    try {
      const invoice = await Invoice.query()
        .findById(id)
        .withGraphFetched('[customer, project, contract]');

      if (!invoice) {
        throw createError.notFound('Invoice', id);
      }

      return invoice;
    } catch (error) {
      logger.error('Failed to fetch invoice', { error, id });
      throw error;
    }
  }

  // 更新发票
  async update(id: string, updateData: Partial<IInvoice>, userId?: string): Promise<Invoice> {
    try {
      logger.info('Updating invoice', { id, updateData, userId });

      const invoice = await this.findById(id);

      // 检查发票状态是否允许修改
      if (invoice.status === 'paid' || invoice.status === 'cancelled') {
        throw createError.business('Cannot modify paid or cancelled invoice', 'INVOICE_MODIFICATION_NOT_ALLOWED');
      }

      // 数据验证和清洗
      const cleanedData = await this.validateAndCleanInvoiceData(updateData);

      // 如果明细发生变化，重新计算金额
      if (cleanedData.lineItems) {
        cleanedData.subtotal = cleanedData.lineItems.reduce((sum, item) => sum + item.totalPrice, 0);
        
        // 重新计算税额
        const taxDetails = await this.taxService.calculateTax(cleanedData);
        cleanedData.taxDetails = taxDetails;
        cleanedData.taxAmount = taxDetails.reduce((sum, tax) => sum + tax.taxAmount, 0);
        
        // 重新计算总额
        cleanedData.totalAmount = cleanedData.subtotal + cleanedData.taxAmount - (cleanedData.discountAmount || 0);
      }

      // 更新发票
      const updatedInvoice = await invoice.$query().patchAndFetch({
        ...cleanedData,
        updatedAt: new Date()
      });

      // 记录活动
      await updatedInvoice.addActivity('updated', 'Invoice information updated', {
        updatedBy: userId
      });

      // 发送事件
      await this.eventBus.publish({
        type: 'invoice.updated',
        aggregateId: id,
        data: { invoice: updatedInvoice, updatedBy: userId },
        timestamp: new Date()
      });

      logger.info('Invoice updated successfully', { invoiceId: id });
      return updatedInvoice;
    } catch (error) {
      logger.error('Failed to update invoice', { error, id, updateData });
      throw error;
    }
  }

  // 发送发票
  async send(id: string, recipients?: string[], message?: string, userId?: string): Promise<Invoice> {
    try {
      const invoice = await this.findById(id);

      if (invoice.status !== 'draft' && invoice.status !== 'pending_approval') {
        throw createError.business('Only draft or pending approval invoices can be sent', 'INVALID_INVOICE_STATUS');
      }

      // 生成发票PDF
      const invoicePdf = await this.templateService.generateInvoicePDF(invoice);

      // 发送发票
      await this.notificationService.sendInvoice(invoice, recipients, invoicePdf, message);

      // 更新状态
      await invoice.send(userId);

      // 发送事件
      await this.eventBus.publish({
        type: 'invoice.sent',
        aggregateId: id,
        data: { invoice, recipients, sentBy: userId },
        timestamp: new Date()
      });

      logger.info('Invoice sent', { invoiceId: id, recipients });
      return await this.findById(id);
    } catch (error) {
      logger.error('Failed to send invoice', { error, id });
      throw error;
    }
  }

  // 处理付款
  async processPayment(id: string, paymentData: any, userId?: string): Promise<Invoice> {
    try {
      const invoice = await this.findById(id);

      // 验证付款数据
      await this.validatePaymentData(paymentData, invoice);

      // 处理付款
      const processedPayment = await this.paymentService.processPayment(paymentData);

      // 添加付款记录
      await invoice.addPayment(processedPayment);

      // 发送事件
      await this.eventBus.publish({
        type: 'invoice.payment_received',
        aggregateId: id,
        data: { invoice, payment: processedPayment, processedBy: userId },
        timestamp: new Date()
      });

      // 发送通知
      await this.notificationService.notifyPaymentReceived(invoice, processedPayment);

      logger.info('Payment processed', { invoiceId: id, amount: processedPayment.amount });
      return await this.findById(id);
    } catch (error) {
      logger.error('Failed to process payment', { error, id, paymentData });
      throw error;
    }
  }

  // 审批发票
  async approve(id: string, approverId: string, comments?: string): Promise<Invoice> {
    try {
      const invoice = await this.findById(id);
      await invoice.approve(approverId, comments);

      // 发送事件
      await this.eventBus.publish({
        type: 'invoice.approved',
        aggregateId: id,
        data: { invoice, approverId, comments },
        timestamp: new Date()
      });

      // 发送通知
      await this.notificationService.notifyInvoiceApproved(invoice);

      logger.info('Invoice approved', { invoiceId: id, approverId });
      return await this.findById(id);
    } catch (error) {
      logger.error('Failed to approve invoice', { error, id });
      throw error;
    }
  }

  // 拒绝发票
  async reject(id: string, approverId: string, reason: string): Promise<Invoice> {
    try {
      const invoice = await this.findById(id);
      await invoice.reject(approverId, reason);

      // 发送事件
      await this.eventBus.publish({
        type: 'invoice.rejected',
        aggregateId: id,
        data: { invoice, approverId, reason },
        timestamp: new Date()
      });

      // 发送通知
      await this.notificationService.notifyInvoiceRejected(invoice, reason);

      logger.info('Invoice rejected', { invoiceId: id, approverId, reason });
      return await this.findById(id);
    } catch (error) {
      logger.error('Failed to reject invoice', { error, id });
      throw error;
    }
  }

  // 取消发票
  async cancel(id: string, reason?: string, userId?: string): Promise<Invoice> {
    try {
      const invoice = await this.findById(id);
      await invoice.cancel(reason, userId);

      // 发送事件
      await this.eventBus.publish({
        type: 'invoice.cancelled',
        aggregateId: id,
        data: { invoice, reason, cancelledBy: userId },
        timestamp: new Date()
      });

      logger.info('Invoice cancelled', { invoiceId: id, reason });
      return await this.findById(id);
    } catch (error) {
      logger.error('Failed to cancel invoice', { error, id });
      throw error;
    }
  }

  // 创建贷项通知单
  async createCreditNote(id: string, creditData: any, userId?: string): Promise<Invoice> {
    try {
      const originalInvoice = await this.findById(id);

      // 创建贷项通知单
      const creditNoteData = {
        type: 'credit_note' as InvoiceType,
        customerId: originalInvoice.customerId,
        projectId: originalInvoice.projectId,
        contractId: originalInvoice.contractId,
        description: creditData.description || `Credit note for invoice ${originalInvoice.invoiceNumber}`,
        lineItems: creditData.lineItems,
        subtotal: creditData.amount,
        currency: originalInvoice.currency,
        issueDate: new Date(),
        dueDate: new Date(),
        metadata: {
          originalInvoiceId: originalInvoice.id,
          originalInvoiceNumber: originalInvoice.invoiceNumber,
          creditReason: creditData.reason
        }
      };

      const creditNote = await this.create(creditNoteData, userId);

      // 添加贷项通知单到原发票
      await originalInvoice.addCreditNote({
        id: creditNote.id,
        amount: creditData.amount,
        reason: creditData.reason
      });

      logger.info('Credit note created', { 
        originalInvoiceId: id, 
        creditNoteId: creditNote.id 
      });

      return creditNote;
    } catch (error) {
      logger.error('Failed to create credit note', { error, id, creditData });
      throw error;
    }
  }

  // 获取财务统计
  async getFinancialStatistics(filters?: any): Promise<any> {
    try {
      const baseQuery = Invoice.query();

      // 应用过滤器
      if (filters?.dateFrom) {
        baseQuery.where('issue_date', '>=', filters.dateFrom);
      }
      if (filters?.dateTo) {
        baseQuery.where('issue_date', '<=', filters.dateTo);
      }
      if (filters?.customerId) {
        baseQuery.where('customer_id', filters.customerId);
      }

      const [
        totalInvoices,
        paidInvoices,
        overdueInvoices,
        revenueSummary,
        outstandingAmount
      ] = await Promise.all([
        baseQuery.clone().resultSize(),
        baseQuery.clone().where('status', 'paid').resultSize(),
        baseQuery.clone().where('status', 'overdue').resultSize(),
        Invoice.getRevenueSummary(filters?.dateFrom, filters?.dateTo),
        Invoice.getOutstandingAmount()
      ]);

      return {
        totalInvoices,
        paidInvoices,
        overdueInvoices,
        paymentRate: totalInvoices > 0 ? Math.round((paidInvoices / totalInvoices) * 100) : 0,
        revenueSummary,
        outstandingAmount
      };
    } catch (error) {
      logger.error('Failed to get financial statistics', { error, filters });
      throw error;
    }
  }

  // 私有方法
  private async validateAndCleanInvoiceData(data: Partial<IInvoice>): Promise<Partial<IInvoice>> {
    const cleaned = { ...data };

    // 验证金额
    if (cleaned.subtotal !== undefined && cleaned.subtotal < 0) {
      throw createError.validation('Subtotal must be non-negative');
    }

    // 验证日期
    if (cleaned.issueDate && cleaned.dueDate && cleaned.issueDate > cleaned.dueDate) {
      throw createError.validation('Due date must be after issue date');
    }

    // 验证明细
    if (cleaned.lineItems && cleaned.lineItems.length === 0) {
      throw createError.validation('Invoice must have at least one line item');
    }

    return cleaned;
  }

  private async generateLineItemsFromProject(project: any): Promise<any[]> {
    const lineItems = [];

    // 从项目任务生成明细
    if (project.tasks) {
      project.tasks.forEach((task: any) => {
        if (task.billable && task.status === 'completed') {
          lineItems.push({
            id: require('uuid').v4(),
            description: task.title,
            quantity: task.hoursSpent || 1,
            unitPrice: task.hourlyRate || 100,
            totalPrice: (task.hoursSpent || 1) * (task.hourlyRate || 100),
            category: 'service'
          });
        }
      });
    }

    // 如果没有任务，使用项目总金额
    if (lineItems.length === 0) {
      lineItems.push({
        id: require('uuid').v4(),
        description: `Project: ${project.name}`,
        quantity: 1,
        unitPrice: project.budget || 0,
        totalPrice: project.budget || 0,
        category: 'project'
      });
    }

    return lineItems;
  }

  private async generateLineItemsFromContract(contract: any): Promise<any[]> {
    const lineItems = [];

    // 从合同条款生成明细
    if (contract.terms) {
      contract.terms.forEach((term: any, index: number) => {
        if (term.type === 'payment') {
          lineItems.push({
            id: require('uuid').v4(),
            description: term.title,
            quantity: 1,
            unitPrice: contract.amount || 0,
            totalPrice: contract.amount || 0,
            category: 'contract'
          });
        }
      });
    }

    // 如果没有条款，使用合同总金额
    if (lineItems.length === 0) {
      lineItems.push({
        id: require('uuid').v4(),
        description: `Contract: ${contract.title}`,
        quantity: 1,
        unitPrice: contract.amount || 0,
        totalPrice: contract.amount || 0,
        category: 'contract'
      });
    }

    return lineItems;
  }

  private calculateDefaultDueDate(): Date {
    const date = new Date();
    date.setDate(date.getDate() + 30); // 默认30天后到期
    return date;
  }

  private async getCustomerBillingAddress(customerId: string): Promise<any> {
    // 这里应该从客户服务获取账单地址
    return {
      name: 'Customer Name',
      address1: 'Address Line 1',
      city: 'City',
      country: 'Country'
    };
  }

  private async validatePaymentData(paymentData: any, invoice: Invoice): Promise<void> {
    if (!paymentData.amount || paymentData.amount <= 0) {
      throw createError.validation('Payment amount must be positive');
    }

    if (paymentData.amount > invoice.remainingAmount) {
      throw createError.validation('Payment amount cannot exceed remaining amount');
    }

    if (!paymentData.method) {
      throw createError.validation('Payment method is required');
    }
  }
}
