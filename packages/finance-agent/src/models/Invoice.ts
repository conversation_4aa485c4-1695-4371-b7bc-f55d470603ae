import { Model } from 'objection';
import { BaseModel } from '@linkagent/shared/models/BaseModel';
import { Invoice as IInvoice, InvoiceStatus, InvoiceType } from '@linkagent/shared/types';

export class Invoice extends BaseModel implements IInvoice {
  static tableName = 'invoices';

  // 基础字段
  id!: string;
  invoiceNumber!: string;
  customerId!: string;
  projectId?: string;
  contractId?: string;
  type!: InvoiceType;
  status!: InvoiceStatus;
  issueDate!: Date;
  dueDate!: Date;
  paidDate?: Date;
  subtotal!: number;
  taxAmount!: number;
  discountAmount!: number;
  totalAmount!: number;
  paidAmount!: number;
  currency!: string;
  exchangeRate?: number;
  baseCurrency?: string;
  baseAmount?: number;
  description?: string;
  notes?: string;
  terms?: string;
  metadata!: Record<string, any>;
  createdAt!: Date;
  updatedAt!: Date;

  // 扩展字段
  lineItems!: Array<{
    id: string;
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    taxRate?: number;
    taxAmount?: number;
    discountRate?: number;
    discountAmount?: number;
    productId?: string;
    serviceId?: string;
    category?: string;
  }>; // 发票明细
  taxDetails?: Array<{
    taxType: string;
    taxRate: number;
    taxableAmount: number;
    taxAmount: number;
    description?: string;
  }>; // 税务详情
  discountDetails?: Array<{
    type: 'percentage' | 'fixed';
    value: number;
    amount: number;
    description?: string;
    appliedTo?: 'subtotal' | 'item';
  }>; // 折扣详情
  paymentTerms?: {
    type: 'net' | 'eom' | 'cod' | 'prepaid';
    days?: number;
    description?: string;
    lateFeeRate?: number;
    earlyPaymentDiscount?: {
      rate: number;
      days: number;
    };
  }; // 付款条款
  billingAddress?: {
    name: string;
    company?: string;
    address1: string;
    address2?: string;
    city: string;
    state?: string;
    postalCode: string;
    country: string;
  }; // 账单地址
  shippingAddress?: {
    name: string;
    company?: string;
    address1: string;
    address2?: string;
    city: string;
    state?: string;
    postalCode: string;
    country: string;
  }; // 收货地址
  attachments?: Array<{
    id: string;
    filename: string;
    fileType: string;
    fileSize: number;
    url: string;
    uploadedAt: Date;
  }>; // 附件
  approvals?: Array<{
    id: string;
    approver: string;
    status: 'pending' | 'approved' | 'rejected';
    approvedAt?: Date;
    comments?: string;
  }>; // 审批记录
  payments?: Array<{
    id: string;
    amount: number;
    currency: string;
    method: string;
    reference?: string;
    paidAt: Date;
    notes?: string;
  }>; // 付款记录
  creditNotes?: Array<{
    id: string;
    amount: number;
    reason: string;
    issuedAt: Date;
    appliedAt?: Date;
  }>; // 贷项通知单
  recurringInfo?: {
    isRecurring: boolean;
    frequency: 'monthly' | 'quarterly' | 'annually';
    nextInvoiceDate?: Date;
    endDate?: Date;
    templateId?: string;
  }; // 循环开票信息
  dunningInfo?: {
    level: number;
    lastDunningDate?: Date;
    nextDunningDate?: Date;
    dunningFee?: number;
    isBlocked?: boolean;
  }; // 催收信息

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['invoiceNumber', 'customerId', 'type', 'status', 'issueDate', 'dueDate', 'subtotal', 'taxAmount', 'discountAmount', 'totalAmount', 'currency', 'lineItems'],
      properties: {
        id: { type: 'string', format: 'uuid' },
        invoiceNumber: { type: 'string', minLength: 1, maxLength: 50 },
        customerId: { type: 'string', format: 'uuid' },
        projectId: { type: ['string', 'null'], format: 'uuid' },
        contractId: { type: ['string', 'null'], format: 'uuid' },
        type: { 
          type: 'string', 
          enum: ['standard', 'proforma', 'credit_note', 'debit_note', 'recurring'] 
        },
        status: { 
          type: 'string', 
          enum: ['draft', 'pending_approval', 'sent', 'viewed', 'partially_paid', 'paid', 'overdue', 'cancelled', 'refunded'] 
        },
        issueDate: { type: 'string', format: 'date' },
        dueDate: { type: 'string', format: 'date' },
        paidDate: { type: ['string', 'null'], format: 'date' },
        subtotal: { type: 'number', minimum: 0 },
        taxAmount: { type: 'number', minimum: 0 },
        discountAmount: { type: 'number', minimum: 0 },
        totalAmount: { type: 'number', minimum: 0 },
        paidAmount: { type: 'number', minimum: 0 },
        currency: { type: 'string', minLength: 3, maxLength: 3 },
        exchangeRate: { type: ['number', 'null'], minimum: 0 },
        baseCurrency: { type: ['string', 'null'], minLength: 3, maxLength: 3 },
        baseAmount: { type: ['number', 'null'], minimum: 0 },
        description: { type: ['string', 'null'], maxLength: 1000 },
        notes: { type: ['string', 'null'], maxLength: 2000 },
        terms: { type: ['string', 'null'], maxLength: 2000 },
        metadata: { type: 'object' },
        lineItems: { type: 'array', minItems: 1 },
        taxDetails: { type: ['array', 'null'] },
        discountDetails: { type: ['array', 'null'] },
        paymentTerms: { type: ['object', 'null'] },
        billingAddress: { type: ['object', 'null'] },
        shippingAddress: { type: ['object', 'null'] },
        attachments: { type: ['array', 'null'] },
        approvals: { type: ['array', 'null'] },
        payments: { type: ['array', 'null'] },
        creditNotes: { type: ['array', 'null'] },
        recurringInfo: { type: ['object', 'null'] },
        dunningInfo: { type: ['object', 'null'] }
      }
    };
  }

  static get relationMappings() {
    return {
      customer: {
        relation: Model.BelongsToOneRelation,
        modelClass: 'Customer',
        join: {
          from: 'invoices.customer_id',
          to: 'customers.id'
        }
      },
      project: {
        relation: Model.BelongsToOneRelation,
        modelClass: 'Project',
        join: {
          from: 'invoices.project_id',
          to: 'projects.id'
        }
      },
      contract: {
        relation: Model.BelongsToOneRelation,
        modelClass: 'Contract',
        join: {
          from: 'invoices.contract_id',
          to: 'contracts.id'
        }
      }
    };
  }

  // 虚拟属性
  get isPaid(): boolean {
    return this.status === 'paid';
  }

  get isOverdue(): boolean {
    return this.status === 'overdue' || (!this.isPaid && new Date() > this.dueDate);
  }

  get remainingAmount(): number {
    return Math.max(0, this.totalAmount - this.paidAmount);
  }

  get paymentProgress(): number {
    return this.totalAmount > 0 ? Math.round((this.paidAmount / this.totalAmount) * 100) : 0;
  }

  get daysOverdue(): number {
    if (!this.isOverdue) return 0;
    return Math.ceil((Date.now() - this.dueDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  get daysToDue(): number {
    if (this.isPaid) return 0;
    return Math.ceil((this.dueDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
  }

  get effectiveTaxRate(): number {
    return this.subtotal > 0 ? (this.taxAmount / this.subtotal) * 100 : 0;
  }

  get effectiveDiscountRate(): number {
    return this.subtotal > 0 ? (this.discountAmount / this.subtotal) * 100 : 0;
  }

  get netAmount(): number {
    return this.subtotal - this.discountAmount;
  }

  // 实例方法
  async send(sentBy?: string): Promise<void> {
    if (this.status !== 'draft' && this.status !== 'pending_approval') {
      throw new Error('Only draft or pending approval invoices can be sent');
    }

    this.status = 'sent';
    await this.$query().patch({
      status: this.status,
      updatedAt: new Date(),
      metadata: {
        ...this.metadata,
        sentInfo: {
          sentAt: new Date(),
          sentBy
        }
      }
    });

    await this.addActivity('sent', 'Invoice sent to customer', { sentBy });
  }

  async markAsViewed(viewedBy?: string): Promise<void> {
    if (this.status === 'sent') {
      this.status = 'viewed';
      await this.$query().patch({
        status: this.status,
        updatedAt: new Date(),
        metadata: {
          ...this.metadata,
          viewedInfo: {
            viewedAt: new Date(),
            viewedBy
          }
        }
      });

      await this.addActivity('viewed', 'Invoice viewed by customer', { viewedBy });
    }
  }

  async addPayment(payment: any): Promise<void> {
    const payments = this.payments || [];
    payments.push({
      ...payment,
      id: payment.id || require('uuid').v4(),
      paidAt: payment.paidAt || new Date()
    });

    // 更新已付金额
    const totalPaid = payments.reduce((sum, p) => sum + p.amount, 0);
    
    // 确定新状态
    let newStatus = this.status;
    if (totalPaid >= this.totalAmount) {
      newStatus = 'paid';
      this.paidDate = new Date();
    } else if (totalPaid > 0) {
      newStatus = 'partially_paid';
    }

    await this.$query().patch({
      payments,
      paidAmount: totalPaid,
      status: newStatus,
      paidDate: this.paidDate,
      updatedAt: new Date()
    });

    await this.addActivity('payment_received', `Payment received: ${payment.amount} ${payment.currency}`, {
      payment
    });
  }

  async addCreditNote(creditNote: any): Promise<void> {
    const creditNotes = this.creditNotes || [];
    creditNotes.push({
      ...creditNote,
      id: creditNote.id || require('uuid').v4(),
      issuedAt: creditNote.issuedAt || new Date()
    });

    await this.$query().patch({
      creditNotes,
      updatedAt: new Date()
    });

    await this.addActivity('credit_note_issued', `Credit note issued: ${creditNote.amount}`, {
      creditNote
    });
  }

  async approve(approverId: string, comments?: string): Promise<void> {
    if (this.status !== 'pending_approval') {
      throw new Error('Only pending approval invoices can be approved');
    }

    const approvals = this.approvals || [];
    approvals.push({
      id: require('uuid').v4(),
      approver: approverId,
      status: 'approved',
      approvedAt: new Date(),
      comments
    });

    this.status = 'draft'; // 审批后回到草稿状态，等待发送

    await this.$query().patch({
      approvals,
      status: this.status,
      updatedAt: new Date()
    });

    await this.addActivity('approved', 'Invoice approved', { approverId, comments });
  }

  async reject(approverId: string, reason: string): Promise<void> {
    if (this.status !== 'pending_approval') {
      throw new Error('Only pending approval invoices can be rejected');
    }

    const approvals = this.approvals || [];
    approvals.push({
      id: require('uuid').v4(),
      approver: approverId,
      status: 'rejected',
      approvedAt: new Date(),
      comments: reason
    });

    this.status = 'draft'; // 拒绝后回到草稿状态

    await this.$query().patch({
      approvals,
      status: this.status,
      updatedAt: new Date()
    });

    await this.addActivity('rejected', 'Invoice rejected', { approverId, reason });
  }

  async cancel(reason?: string, cancelledBy?: string): Promise<void> {
    if (this.isPaid) {
      throw new Error('Paid invoices cannot be cancelled');
    }

    this.status = 'cancelled';
    await this.$query().patch({
      status: this.status,
      updatedAt: new Date(),
      metadata: {
        ...this.metadata,
        cancellationInfo: {
          cancelledAt: new Date(),
          cancelledBy,
          reason
        }
      }
    });

    await this.addActivity('cancelled', `Invoice cancelled: ${reason || 'No reason provided'}`, {
      cancelledBy,
      reason
    });
  }

  async markAsOverdue(): Promise<void> {
    if (!this.isPaid && new Date() > this.dueDate && this.status !== 'overdue') {
      this.status = 'overdue';
      await this.$query().patch({
        status: this.status,
        updatedAt: new Date()
      });

      await this.addActivity('marked_overdue', 'Invoice marked as overdue');
    }
  }

  async updateDunningInfo(level: number, nextDunningDate?: Date): Promise<void> {
    const dunningInfo = {
      ...this.dunningInfo,
      level,
      lastDunningDate: new Date(),
      nextDunningDate
    };

    await this.$query().patch({
      dunningInfo,
      updatedAt: new Date()
    });

    await this.addActivity('dunning_updated', `Dunning level updated to ${level}`, {
      dunningInfo
    });
  }

  async recalculateAmounts(): Promise<void> {
    // 重新计算小计
    const subtotal = this.lineItems.reduce((sum, item) => sum + item.totalPrice, 0);
    
    // 重新计算税额
    const taxAmount = this.taxDetails?.reduce((sum, tax) => sum + tax.taxAmount, 0) || 0;
    
    // 重新计算折扣
    const discountAmount = this.discountDetails?.reduce((sum, discount) => sum + discount.amount, 0) || 0;
    
    // 计算总额
    const totalAmount = subtotal + taxAmount - discountAmount;

    await this.$query().patch({
      subtotal,
      taxAmount,
      discountAmount,
      totalAmount,
      updatedAt: new Date()
    });
  }

  async addActivity(type: string, description: string, metadata?: Record<string, any>): Promise<void> {
    // 这里应该添加到发票活动表
    console.log('Invoice activity:', {
      invoiceId: this.id,
      type,
      description,
      metadata,
      timestamp: new Date()
    });
  }

  // 静态方法
  static async findByCustomer(customerId: string): Promise<Invoice[]> {
    return this.query()
      .where('customer_id', customerId)
      .orderBy('issue_date', 'desc');
  }

  static async findByProject(projectId: string): Promise<Invoice[]> {
    return this.query()
      .where('project_id', projectId)
      .orderBy('issue_date', 'desc');
  }

  static async findByStatus(status: InvoiceStatus): Promise<Invoice[]> {
    return this.query()
      .where('status', status)
      .orderBy('due_date', 'asc');
  }

  static async findOverdue(): Promise<Invoice[]> {
    return this.query()
      .where('due_date', '<', new Date())
      .where('status', 'not in', ['paid', 'cancelled', 'refunded'])
      .orderBy('due_date', 'asc');
  }

  static async findDueSoon(days: number = 7): Promise<Invoice[]> {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    return this.query()
      .where('due_date', '<=', futureDate)
      .where('status', 'not in', ['paid', 'cancelled', 'refunded'])
      .orderBy('due_date', 'asc');
  }

  static async findRecurring(): Promise<Invoice[]> {
    return this.query()
      .whereRaw('recurring_info->\'isRecurring\' = ?', [true])
      .orderBy('created_at', 'desc');
  }

  static async generateInvoiceNumber(type: InvoiceType = 'standard'): Promise<string> {
    const prefix = type === 'credit_note' ? 'CN' : 'INV';
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    
    // 获取当月的发票数量
    const count = await this.query()
      .where('invoice_number', 'like', `${prefix}${year}${month}%`)
      .resultSize();

    const sequence = String(count + 1).padStart(4, '0');
    return `${prefix}${year}${month}${sequence}`;
  }

  static async getRevenueSummary(dateFrom?: Date, dateTo?: Date): Promise<any> {
    let query = this.query()
      .where('status', 'paid');

    if (dateFrom) {
      query = query.where('paid_date', '>=', dateFrom);
    }

    if (dateTo) {
      query = query.where('paid_date', '<=', dateTo);
    }

    const results = await query
      .groupBy('currency')
      .sum('total_amount as totalRevenue')
      .count('* as invoiceCount')
      .select('currency');

    return results.reduce((summary, result) => {
      summary[result.currency] = {
        totalRevenue: parseFloat(result.totalRevenue),
        invoiceCount: parseInt(result.invoiceCount)
      };
      return summary;
    }, {});
  }

  static async getOutstandingAmount(): Promise<any> {
    const results = await this.query()
      .where('status', 'not in', ['paid', 'cancelled', 'refunded'])
      .groupBy('currency')
      .sum('total_amount - paid_amount as outstandingAmount')
      .count('* as invoiceCount')
      .select('currency');

    return results.reduce((summary, result) => {
      summary[result.currency] = {
        outstandingAmount: parseFloat(result.outstandingAmount),
        invoiceCount: parseInt(result.invoiceCount)
      };
      return summary;
    }, {});
  }
}
