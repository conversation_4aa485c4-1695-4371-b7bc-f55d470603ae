import { Router } from 'express';
import { body, param, query } from 'express-validator';
import { LeadService } from '../services/LeadService';
import { LeadScoringService } from '../services/LeadScoringService';
import { validateRequest } from '../middleware/validation';
import { requirePermission } from '../middleware/auth';
import { asyncHandler } from '../middleware/error-handler';
import { logger } from '../utils/logger';

const router = Router();
const leadService = new LeadService();
const scoringService = new LeadScoringService();

// 获取线索列表
router.get('/',
  requirePermission('lead', 'read'),
  [
    query('page').optional().isInt({ min: 1 }).toInt(),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('sortBy').optional().isString(),
    query('sortOrder').optional().isIn(['asc', 'desc']),
    query('search').optional().isString().trim(),
    query('status').optional().isIn(['new', 'contacted', 'qualified', 'unqualified', 'converted', 'lost']),
    query('source').optional().isIn(['website', 'social_media', 'email', 'phone', 'referral', 'advertisement', 'event', 'partner', 'other']),
    query('priority').optional().isIn(['low', 'medium', 'high', 'urgent']),
    query('assignedTo').optional().isUUID(),
    query('campaignId').optional().isUUID(),
    query('scoreMin').optional().isInt({ min: 0, max: 100 }).toInt(),
    query('scoreMax').optional().isInt({ min: 0, max: 100 }).toInt(),
    query('createdAfter').optional().isISO8601().toDate(),
    query('createdBefore').optional().isISO8601().toDate()
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const result = await leadService.findAll(req.query);
    
    res.json({
      success: true,
      data: result.data,
      meta: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev
      }
    });
  })
);

// 获取单个线索
router.get('/:id',
  requirePermission('lead', 'read'),
  [
    param('id').isUUID().withMessage('Invalid lead ID')
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const lead = await leadService.findById(req.params.id);
    
    res.json({
      success: true,
      data: lead
    });
  })
);

// 创建线索
router.post('/',
  requirePermission('lead', 'create'),
  [
    body('source').isIn(['website', 'social_media', 'email', 'phone', 'referral', 'advertisement', 'event', 'partner', 'other']),
    body('title').isString().trim().isLength({ min: 1, max: 255 }),
    body('description').optional().isString().trim().isLength({ max: 2000 }),
    body('status').optional().isIn(['new', 'contacted', 'qualified', 'unqualified', 'converted', 'lost']),
    body('priority').optional().isIn(['low', 'medium', 'high', 'urgent']),
    body('contactInfo').isObject(),
    body('contactInfo.email').optional().isEmail().normalizeEmail(),
    body('contactInfo.phone').optional().isString().trim(),
    body('contactInfo.mobile').optional().isString().trim(),
    body('contactInfo.fax').optional().isString().trim(),
    body('contactInfo.wechat').optional().isString().trim(),
    body('contactInfo.qq').optional().isString().trim(),
    body('assignedTo').optional().isUUID(),
    body('tags').optional().isArray(),
    body('tags.*').optional().isString().trim(),
    body('metadata').optional().isObject(),
    body('campaignId').optional().isUUID(),
    body('referralSource').optional().isString().trim(),
    body('landingPage').optional().isURL(),
    body('utmSource').optional().isString().trim(),
    body('utmMedium').optional().isString().trim(),
    body('utmCampaign').optional().isString().trim(),
    body('utmTerm').optional().isString().trim(),
    body('utmContent').optional().isString().trim(),
    body('industry').optional().isString().trim(),
    body('companySize').optional().isIn(['startup', 'small', 'medium', 'large', 'enterprise']),
    body('jobTitle').optional().isString().trim(),
    body('department').optional().isString().trim(),
    body('decisionMaker').optional().isBoolean(),
    body('budget').optional().isNumeric().toFloat(),
    body('timeline').optional().isString().trim(),
    body('painPoints').optional().isArray(),
    body('painPoints.*').optional().isString().trim(),
    body('interests').optional().isArray(),
    body('interests.*').optional().isString().trim(),
    body('estimatedValue').optional().isNumeric().toFloat(),
    body('currency').optional().isLength({ min: 3, max: 3 })
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const lead = await leadService.create(req.body, req.user?.id);
    
    logger.info('Lead created', { leadId: lead.id, userId: req.user?.id });
    
    res.status(201).json({
      success: true,
      data: lead,
      message: 'Lead created successfully'
    });
  })
);

// 更新线索
router.put('/:id',
  requirePermission('lead', 'update'),
  [
    param('id').isUUID().withMessage('Invalid lead ID'),
    body('source').optional().isIn(['website', 'social_media', 'email', 'phone', 'referral', 'advertisement', 'event', 'partner', 'other']),
    body('title').optional().isString().trim().isLength({ min: 1, max: 255 }),
    body('description').optional().isString().trim().isLength({ max: 2000 }),
    body('status').optional().isIn(['new', 'contacted', 'qualified', 'unqualified', 'converted', 'lost']),
    body('priority').optional().isIn(['low', 'medium', 'high', 'urgent']),
    body('contactInfo').optional().isObject(),
    body('contactInfo.email').optional().isEmail().normalizeEmail(),
    body('contactInfo.phone').optional().isString().trim(),
    body('contactInfo.mobile').optional().isString().trim(),
    body('assignedTo').optional().isUUID(),
    body('tags').optional().isArray(),
    body('tags.*').optional().isString().trim(),
    body('metadata').optional().isObject(),
    body('industry').optional().isString().trim(),
    body('companySize').optional().isIn(['startup', 'small', 'medium', 'large', 'enterprise']),
    body('jobTitle').optional().isString().trim(),
    body('department').optional().isString().trim(),
    body('decisionMaker').optional().isBoolean(),
    body('budget').optional().isNumeric().toFloat(),
    body('timeline').optional().isString().trim(),
    body('painPoints').optional().isArray(),
    body('interests').optional().isArray(),
    body('estimatedValue').optional().isNumeric().toFloat(),
    body('currency').optional().isLength({ min: 3, max: 3 })
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const lead = await leadService.update(req.params.id, req.body, req.user?.id);
    
    logger.info('Lead updated', { leadId: req.params.id, userId: req.user?.id });
    
    res.json({
      success: true,
      data: lead,
      message: 'Lead updated successfully'
    });
  })
);

// 删除线索
router.delete('/:id',
  requirePermission('lead', 'delete'),
  [
    param('id').isUUID().withMessage('Invalid lead ID')
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    await leadService.delete(req.params.id, req.user?.id);
    
    logger.info('Lead deleted', { leadId: req.params.id, userId: req.user?.id });
    
    res.json({
      success: true,
      message: 'Lead deleted successfully'
    });
  })
);

// 分配线索
router.post('/:id/assign',
  requirePermission('lead', 'assign'),
  [
    param('id').isUUID().withMessage('Invalid lead ID'),
    body('assigneeId').isUUID().withMessage('Invalid assignee ID')
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const lead = await leadService.assign(req.params.id, req.body.assigneeId, req.user?.id);
    
    logger.info('Lead assigned', { 
      leadId: req.params.id, 
      assigneeId: req.body.assigneeId, 
      userId: req.user?.id 
    });
    
    res.json({
      success: true,
      data: lead,
      message: 'Lead assigned successfully'
    });
  })
);

// 转换线索为商机
router.post('/:id/convert',
  requirePermission('lead', 'convert'),
  [
    param('id').isUUID().withMessage('Invalid lead ID'),
    body('opportunityData').isObject(),
    body('opportunityData.name').isString().trim().isLength({ min: 1, max: 255 }),
    body('opportunityData.amount').optional().isNumeric().toFloat(),
    body('opportunityData.currency').optional().isLength({ min: 3, max: 3 }),
    body('opportunityData.expectedCloseDate').optional().isISO8601().toDate(),
    body('opportunityData.probability').optional().isInt({ min: 0, max: 100 }).toInt()
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const lead = await leadService.convert(req.params.id, req.body.opportunityData, req.user?.id);
    
    logger.info('Lead converted', { leadId: req.params.id, userId: req.user?.id });
    
    res.json({
      success: true,
      data: lead,
      message: 'Lead converted to opportunity successfully'
    });
  })
);

// 批量更新线索
router.patch('/bulk',
  requirePermission('lead', 'update'),
  [
    body('ids').isArray().withMessage('IDs must be an array'),
    body('ids.*').isUUID().withMessage('Invalid lead ID'),
    body('updateData').isObject().withMessage('Update data is required'),
    body('updateData.status').optional().isIn(['new', 'contacted', 'qualified', 'unqualified', 'converted', 'lost']),
    body('updateData.priority').optional().isIn(['low', 'medium', 'high', 'urgent']),
    body('updateData.assignedTo').optional().isUUID(),
    body('updateData.tags').optional().isArray()
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const leads = await leadService.bulkUpdate(req.body.ids, req.body.updateData, req.user?.id);
    
    logger.info('Leads bulk updated', { 
      count: leads.length, 
      userId: req.user?.id 
    });
    
    res.json({
      success: true,
      data: leads,
      message: `${leads.length} leads updated successfully`
    });
  })
);

// 批量分配线索
router.post('/bulk/assign',
  requirePermission('lead', 'assign'),
  [
    body('ids').isArray().withMessage('IDs must be an array'),
    body('ids.*').isUUID().withMessage('Invalid lead ID'),
    body('assigneeId').isUUID().withMessage('Invalid assignee ID')
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const leads = await leadService.bulkAssign(req.body.ids, req.body.assigneeId, req.user?.id);
    
    logger.info('Leads bulk assigned', { 
      count: leads.length, 
      assigneeId: req.body.assigneeId,
      userId: req.user?.id 
    });
    
    res.json({
      success: true,
      data: leads,
      message: `${leads.length} leads assigned successfully`
    });
  })
);

// 获取线索统计
router.get('/stats/overview',
  requirePermission('lead', 'read'),
  [
    query('assignedTo').optional().isUUID(),
    query('dateFrom').optional().isISO8601().toDate(),
    query('dateTo').optional().isISO8601().toDate()
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    const stats = await leadService.getStatistics(req.query);
    
    res.json({
      success: true,
      data: stats
    });
  })
);

// 重新计算评分
router.post('/scoring/recalculate',
  requirePermission('lead', 'update'),
  [
    body('leadIds').optional().isArray(),
    body('leadIds.*').optional().isUUID()
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    await scoringService.recalculateScores(req.body.leadIds);
    
    logger.info('Lead scores recalculated', { 
      leadIds: req.body.leadIds,
      userId: req.user?.id 
    });
    
    res.json({
      success: true,
      message: 'Lead scores recalculated successfully'
    });
  })
);

// 获取评分分布
router.get('/scoring/distribution',
  requirePermission('lead', 'read'),
  asyncHandler(async (req, res) => {
    const distribution = await scoringService.getScoreDistribution();
    
    res.json({
      success: true,
      data: distribution
    });
  })
);

// 获取评分规则
router.get('/scoring/rules',
  requirePermission('lead', 'read'),
  asyncHandler(async (req, res) => {
    const rules = scoringService.getScoringRules();
    
    res.json({
      success: true,
      data: rules.map(rule => ({
        name: rule.name,
        weight: rule.weight
      }))
    });
  })
);

// 更新评分规则权重
router.put('/scoring/rules',
  requirePermission('lead', 'admin'),
  [
    body('weights').isObject().withMessage('Weights must be an object'),
    body('weights.*').isFloat({ min: 0, max: 1 }).withMessage('Weight must be between 0 and 1')
  ],
  validateRequest,
  asyncHandler(async (req, res) => {
    scoringService.updateRuleWeights(req.body.weights);
    
    logger.info('Scoring rule weights updated', { 
      weights: req.body.weights,
      userId: req.user?.id 
    });
    
    res.json({
      success: true,
      message: 'Scoring rule weights updated successfully'
    });
  })
);

export default router;
