import 'express-async-errors';
import express from 'express';
import { createServer } from 'http';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';

import { config } from './config';
import { logger } from './utils/logger';
import { errorHandler } from './middleware/error-handler';
import { authMiddleware } from './middleware/auth';
import { requestLogger } from './middleware/request-logger';
import { setupDatabase } from './database';
import { setupRedis } from './utils/redis';
import { setupQueues } from './utils/queue';
import { setupCron } from './utils/cron';
import { healthCheck } from './utils/health-check';

// 导入路由
import leadRoutes from './routes/leads';
import sourceRoutes from './routes/sources';
import campaignRoutes from './routes/campaigns';
import scoringRoutes from './routes/scoring';
import assignmentRoutes from './routes/assignment';
import followupRoutes from './routes/followup';
import analyticsRoutes from './routes/analytics';
import webhookRoutes from './routes/webhooks';

class LeadAgentApplication {
  public app: express.Application;
  public server: any;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
  }

  // 初始化应用
  public async initialize(): Promise<void> {
    try {
      logger.info('Initializing Lead Agent Service...');

      // 设置基础中间件
      this.setupMiddleware();

      // 设置数据库连接
      await setupDatabase();
      logger.info('Database connected successfully');

      // 设置Redis连接
      await setupRedis();
      logger.info('Redis connected successfully');

      // 设置消息队列
      await setupQueues();
      logger.info('Message queues initialized');

      // 设置定时任务
      setupCron();
      logger.info('Cron jobs initialized');

      // 设置路由
      this.setupRoutes();

      // 设置错误处理
      this.setupErrorHandling();

      logger.info('Lead Agent Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Lead Agent Service:', error);
      process.exit(1);
    }
  }

  // 设置中间件
  private setupMiddleware(): void {
    // 安全中间件
    this.app.use(helmet());

    // CORS中间件
    this.app.use(cors({
      origin: config.cors.origin,
      credentials: config.cors.credentials
    }));

    // 压缩中间件
    this.app.use(compression());

    // 请求体解析
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 限流中间件
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 100, // 限制每个IP 15分钟内最多100个请求
      message: {
        success: false,
        error: {
          code: 'TOO_MANY_REQUESTS',
          message: 'Too many requests from this IP'
        }
      }
    });
    this.app.use('/api', limiter);

    // 日志中间件
    if (config.env !== 'test') {
      this.app.use(morgan('combined', {
        stream: { write: (message) => logger.info(message.trim()) }
      }));
    }

    // 自定义中间件
    this.app.use(requestLogger);
  }

  // 设置路由
  private setupRoutes(): void {
    const apiPrefix = `/api/${config.api.version}`;

    // 健康检查
    this.app.get('/health', healthCheck);
    this.app.get('/ready', healthCheck);

    // API路由
    this.app.use(`${apiPrefix}/leads`, authMiddleware, leadRoutes);
    this.app.use(`${apiPrefix}/sources`, authMiddleware, sourceRoutes);
    this.app.use(`${apiPrefix}/campaigns`, authMiddleware, campaignRoutes);
    this.app.use(`${apiPrefix}/scoring`, authMiddleware, scoringRoutes);
    this.app.use(`${apiPrefix}/assignment`, authMiddleware, assignmentRoutes);
    this.app.use(`${apiPrefix}/followup`, authMiddleware, followupRoutes);
    this.app.use(`${apiPrefix}/analytics`, authMiddleware, analyticsRoutes);
    this.app.use(`${apiPrefix}/webhooks`, webhookRoutes);

    // 404处理
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: `Route ${req.method} ${req.originalUrl} not found`
        }
      });
    });
  }

  // 设置错误处理
  private setupErrorHandling(): void {
    this.app.use(errorHandler);

    // 未捕获异常处理
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      this.gracefulShutdown('SIGTERM');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      this.gracefulShutdown('SIGTERM');
    });

    // 优雅关闭
    process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => this.gracefulShutdown('SIGINT'));
  }

  // 启动服务器
  public async start(): Promise<void> {
    await this.initialize();

    this.server.listen(config.port, () => {
      logger.info(`🚀 Lead Agent Service started on port ${config.port}`);
      logger.info(`🏥 Health Check: http://localhost:${config.port}/health`);
      logger.info(`🌍 Environment: ${config.env}`);
    });
  }

  // 优雅关闭
  private async gracefulShutdown(signal: string): Promise<void> {
    logger.info(`Received ${signal}. Starting graceful shutdown...`);

    this.server.close(async () => {
      logger.info('HTTP server closed');

      try {
        // 关闭数据库连接
        // await database.destroy();
        logger.info('Database connections closed');

        // 关闭Redis连接
        // await redis.disconnect();
        logger.info('Redis connections closed');

        // 关闭消息队列
        // await queue.close();
        logger.info('Message queues closed');

        logger.info('Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('Error during graceful shutdown:', error);
        process.exit(1);
      }
    });

    // 强制关闭超时
    setTimeout(() => {
      logger.error('Graceful shutdown timeout, forcing exit');
      process.exit(1);
    }, 30000);
  }
}

// 启动应用
const app = new LeadAgentApplication();

if (require.main === module) {
  app.start().catch((error) => {
    logger.error('Failed to start Lead Agent Service:', error);
    process.exit(1);
  });
}

export default app;
