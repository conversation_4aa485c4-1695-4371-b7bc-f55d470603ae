import axios from 'axios';
import { Lead } from '../models/Lead';
import { logger } from '../utils/logger';
import { config } from '../config';

// 数据丰富化接口
interface EnrichmentProvider {
  name: string;
  enrichLead(lead: Partial<Lead>): Promise<Partial<Lead>>;
}

// 公司信息接口
interface CompanyInfo {
  name?: string;
  domain?: string;
  industry?: string;
  size?: string;
  revenue?: number;
  employees?: number;
  location?: {
    country?: string;
    city?: string;
    address?: string;
  };
  description?: string;
  founded?: number;
  website?: string;
  socialProfiles?: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
  };
}

// 联系人信息接口
interface ContactInfo {
  firstName?: string;
  lastName?: string;
  fullName?: string;
  jobTitle?: string;
  department?: string;
  seniority?: string;
  email?: string;
  phone?: string;
  linkedin?: string;
  twitter?: string;
}

export class LeadEnrichmentService {
  private providers: EnrichmentProvider[];

  constructor() {
    this.providers = [
      new EmailEnrichmentProvider(),
      new DomainEnrichmentProvider(),
      new SocialEnrichmentProvider(),
      new GeoLocationProvider(),
      new IndustryClassificationProvider()
    ];
  }

  // 丰富线索数据
  async enrichLead(lead: Partial<Lead>): Promise<Partial<Lead>> {
    try {
      logger.debug('Starting lead enrichment', { leadId: lead.id });

      let enrichedLead = { ...lead };

      // 依次应用各个丰富化提供者
      for (const provider of this.providers) {
        try {
          enrichedLead = await provider.enrichLead(enrichedLead);
          logger.debug(`Enrichment completed by ${provider.name}`, { leadId: lead.id });
        } catch (error) {
          logger.warn(`Enrichment failed for provider ${provider.name}`, { 
            error, 
            leadId: lead.id 
          });
        }
      }

      // 计算数据完整度
      enrichedLead.metadata = {
        ...enrichedLead.metadata,
        enrichment: {
          completedAt: new Date(),
          completeness: this.calculateCompleteness(enrichedLead),
          sources: this.providers.map(p => p.name)
        }
      };

      logger.debug('Lead enrichment completed', { 
        leadId: lead.id,
        completeness: enrichedLead.metadata.enrichment.completeness
      });

      return enrichedLead;
    } catch (error) {
      logger.error('Lead enrichment failed', { error, leadId: lead.id });
      return lead; // 返回原始数据
    }
  }

  // 批量丰富化
  async enrichLeads(leads: Partial<Lead>[]): Promise<Partial<Lead>[]> {
    const batchSize = 10;
    const enrichedLeads: Partial<Lead>[] = [];

    for (let i = 0; i < leads.length; i += batchSize) {
      const batch = leads.slice(i, i + batchSize);
      
      const batchResults = await Promise.allSettled(
        batch.map(lead => this.enrichLead(lead))
      );

      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          enrichedLeads.push(result.value);
        } else {
          logger.error('Batch enrichment failed for lead', { 
            error: result.reason,
            leadId: batch[index].id
          });
          enrichedLeads.push(batch[index]); // 使用原始数据
        }
      });

      // 避免API限流
      if (i + batchSize < leads.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return enrichedLeads;
  }

  // 计算数据完整度
  private calculateCompleteness(lead: Partial<Lead>): number {
    const fields = [
      'title', 'description', 'contactInfo.email', 'contactInfo.phone',
      'industry', 'companySize', 'jobTitle', 'department',
      'budget', 'timeline', 'painPoints', 'interests'
    ];

    let completedFields = 0;
    
    fields.forEach(field => {
      const value = this.getNestedValue(lead, field);
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          completedFields++;
        } else if (!Array.isArray(value)) {
          completedFields++;
        }
      }
    });

    return Math.round((completedFields / fields.length) * 100);
  }

  // 获取嵌套属性值
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
}

// 邮箱丰富化提供者
class EmailEnrichmentProvider implements EnrichmentProvider {
  name = 'email_enrichment';

  async enrichLead(lead: Partial<Lead>): Promise<Partial<Lead>> {
    const email = lead.contactInfo?.email;
    if (!email) return lead;

    try {
      // 从邮箱推断公司域名
      const domain = email.split('@')[1];
      if (domain && !lead.metadata?.companyDomain) {
        lead.metadata = {
          ...lead.metadata,
          companyDomain: domain
        };
      }

      // 从邮箱推断姓名（如果邮箱格式为 <EMAIL>）
      const localPart = email.split('@')[0];
      if (localPart.includes('.') && !lead.metadata?.inferredName) {
        const parts = localPart.split('.');
        if (parts.length === 2) {
          lead.metadata = {
            ...lead.metadata,
            inferredName: {
              firstName: this.capitalize(parts[0]),
              lastName: this.capitalize(parts[1])
            }
          };
        }
      }

      return lead;
    } catch (error) {
      logger.warn('Email enrichment failed', { error, email });
      return lead;
    }
  }

  private capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  }
}

// 域名丰富化提供者
class DomainEnrichmentProvider implements EnrichmentProvider {
  name = 'domain_enrichment';

  async enrichLead(lead: Partial<Lead>): Promise<Partial<Lead>> {
    const domain = lead.metadata?.companyDomain;
    if (!domain) return lead;

    try {
      // 这里可以集成第三方API如Clearbit、FullContact等
      // 示例：获取公司信息
      const companyInfo = await this.getCompanyInfo(domain);
      
      if (companyInfo) {
        // 更新行业信息
        if (companyInfo.industry && !lead.industry) {
          lead.industry = companyInfo.industry;
        }

        // 更新公司规模
        if (companyInfo.size && !lead.companySize) {
          lead.companySize = companyInfo.size;
        }

        // 保存公司详细信息
        lead.metadata = {
          ...lead.metadata,
          companyInfo
        };
      }

      return lead;
    } catch (error) {
      logger.warn('Domain enrichment failed', { error, domain });
      return lead;
    }
  }

  private async getCompanyInfo(domain: string): Promise<CompanyInfo | null> {
    try {
      // 这里应该调用真实的API，如Clearbit Company API
      // const response = await axios.get(`https://company.clearbit.com/v2/companies/find?domain=${domain}`, {
      //   headers: { Authorization: `Bearer ${config.clearbit.apiKey}` }
      // });
      
      // 模拟API响应
      const mockResponse = {
        name: `Company for ${domain}`,
        domain,
        industry: 'Technology',
        size: 'medium',
        employees: 100,
        website: `https://${domain}`
      };

      return mockResponse;
    } catch (error) {
      logger.debug('Company info API failed', { error, domain });
      return null;
    }
  }
}

// 社交媒体丰富化提供者
class SocialEnrichmentProvider implements EnrichmentProvider {
  name = 'social_enrichment';

  async enrichLead(lead: Partial<Lead>): Promise<Partial<Lead>> {
    const email = lead.contactInfo?.email;
    if (!email) return lead;

    try {
      // 这里可以集成LinkedIn API、Twitter API等
      const socialProfiles = await this.findSocialProfiles(email);
      
      if (socialProfiles) {
        lead.metadata = {
          ...lead.metadata,
          socialProfiles
        };

        // 从LinkedIn更新职位信息
        if (socialProfiles.linkedin && !lead.jobTitle) {
          const linkedinInfo = await this.getLinkedInInfo(socialProfiles.linkedin);
          if (linkedinInfo?.jobTitle) {
            lead.jobTitle = linkedinInfo.jobTitle;
          }
          if (linkedinInfo?.department) {
            lead.department = linkedinInfo.department;
          }
        }
      }

      return lead;
    } catch (error) {
      logger.warn('Social enrichment failed', { error, email });
      return lead;
    }
  }

  private async findSocialProfiles(email: string): Promise<any> {
    // 模拟社交媒体查找
    return {
      linkedin: `https://linkedin.com/in/${email.split('@')[0]}`,
      twitter: `https://twitter.com/${email.split('@')[0]}`
    };
  }

  private async getLinkedInInfo(linkedinUrl: string): Promise<ContactInfo | null> {
    // 这里应该调用LinkedIn API或第三方服务
    return {
      jobTitle: 'Software Engineer',
      department: 'Engineering'
    };
  }
}

// 地理位置提供者
class GeoLocationProvider implements EnrichmentProvider {
  name = 'geo_location';

  async enrichLead(lead: Partial<Lead>): Promise<Partial<Lead>> {
    const ipAddress = lead.ipAddress;
    if (!ipAddress) return lead;

    try {
      const geoInfo = await this.getGeoLocation(ipAddress);
      
      if (geoInfo) {
        lead.geoLocation = geoInfo;
        
        // 如果没有时区信息，根据地理位置推断
        if (!lead.metadata?.timezone) {
          lead.metadata = {
            ...lead.metadata,
            timezone: this.inferTimezone(geoInfo)
          };
        }
      }

      return lead;
    } catch (error) {
      logger.warn('Geo location enrichment failed', { error, ipAddress });
      return lead;
    }
  }

  private async getGeoLocation(ipAddress: string): Promise<any> {
    try {
      // 使用免费的IP地理位置API
      const response = await axios.get(`http://ip-api.com/json/${ipAddress}`);
      
      if (response.data.status === 'success') {
        return {
          country: response.data.country,
          countryCode: response.data.countryCode,
          region: response.data.regionName,
          city: response.data.city,
          latitude: response.data.lat,
          longitude: response.data.lon,
          timezone: response.data.timezone
        };
      }
      
      return null;
    } catch (error) {
      logger.debug('Geo location API failed', { error, ipAddress });
      return null;
    }
  }

  private inferTimezone(geoInfo: any): string {
    // 简单的时区推断逻辑
    if (geoInfo.timezone) return geoInfo.timezone;
    
    const timezoneMap: Record<string, string> = {
      'CN': 'Asia/Shanghai',
      'US': 'America/New_York',
      'JP': 'Asia/Tokyo',
      'GB': 'Europe/London',
      'DE': 'Europe/Berlin'
    };
    
    return timezoneMap[geoInfo.countryCode] || 'UTC';
  }
}

// 行业分类提供者
class IndustryClassificationProvider implements EnrichmentProvider {
  name = 'industry_classification';

  async enrichLead(lead: Partial<Lead>): Promise<Partial<Lead>> {
    if (lead.industry) return lead; // 已有行业信息

    try {
      // 基于公司域名、描述等推断行业
      const industry = await this.classifyIndustry(lead);
      
      if (industry) {
        lead.industry = industry;
        
        // 基于行业设置默认标签
        const industryTags = this.getIndustryTags(industry);
        if (industryTags.length > 0) {
          lead.tags = [...(lead.tags || []), ...industryTags];
        }
      }

      return lead;
    } catch (error) {
      logger.warn('Industry classification failed', { error, leadId: lead.id });
      return lead;
    }
  }

  private async classifyIndustry(lead: Partial<Lead>): Promise<string | null> {
    const domain = lead.metadata?.companyDomain;
    const description = lead.description || '';
    const title = lead.title || '';
    
    // 简单的关键词匹配
    const keywords = `${domain} ${description} ${title}`.toLowerCase();
    
    if (keywords.includes('tech') || keywords.includes('software') || keywords.includes('app')) {
      return 'Technology';
    }
    if (keywords.includes('finance') || keywords.includes('bank') || keywords.includes('investment')) {
      return 'Financial Services';
    }
    if (keywords.includes('health') || keywords.includes('medical') || keywords.includes('hospital')) {
      return 'Healthcare';
    }
    if (keywords.includes('education') || keywords.includes('school') || keywords.includes('university')) {
      return 'Education';
    }
    if (keywords.includes('retail') || keywords.includes('shop') || keywords.includes('store')) {
      return 'Retail';
    }
    
    return null;
  }

  private getIndustryTags(industry: string): string[] {
    const tagMap: Record<string, string[]> = {
      'Technology': ['tech', 'software', 'digital'],
      'Financial Services': ['finance', 'banking', 'fintech'],
      'Healthcare': ['health', 'medical', 'pharma'],
      'Education': ['education', 'learning', 'academic'],
      'Retail': ['retail', 'ecommerce', 'consumer']
    };
    
    return tagMap[industry] || [];
  }
}
