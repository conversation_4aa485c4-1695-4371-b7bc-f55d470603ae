import { Lead } from '../models/Lead';
import { logger } from '../utils/logger';
import { config } from '../config';
import OpenAI from 'openai';

// 评分规则接口
interface ScoringRule {
  name: string;
  weight: number;
  calculate: (lead: Partial<Lead>) => number;
}

// 评分配置
interface ScoringConfig {
  rules: ScoringRule[];
  aiEnabled: boolean;
  aiWeight: number;
}

export class LeadScoringService {
  private openai?: OpenAI;
  private scoringConfig: ScoringConfig;

  constructor() {
    if (config.ai.openai.apiKey) {
      this.openai = new OpenAI({
        apiKey: config.ai.openai.apiKey
      });
    }

    this.scoringConfig = {
      rules: this.initializeScoringRules(),
      aiEnabled: !!config.ai.openai.apiKey && config.features.aiAssistant,
      aiWeight: 0.3 // AI评分占总分的30%
    };
  }

  // 计算线索评分
  async calculateScore(lead: Partial<Lead>): Promise<number> {
    try {
      logger.debug('Calculating lead score', { leadId: lead.id });

      // 基础规则评分
      const ruleScore = this.calculateRuleBasedScore(lead);
      
      // AI评分（如果启用）
      let aiScore = 0;
      if (this.scoringConfig.aiEnabled && this.openai) {
        aiScore = await this.calculateAIScore(lead);
      }

      // 综合评分
      const finalScore = this.scoringConfig.aiEnabled 
        ? Math.round(ruleScore * (1 - this.scoringConfig.aiWeight) + aiScore * this.scoringConfig.aiWeight)
        : ruleScore;

      // 确保评分在0-100范围内
      const normalizedScore = Math.max(0, Math.min(100, finalScore));

      logger.debug('Lead score calculated', {
        leadId: lead.id,
        ruleScore,
        aiScore,
        finalScore: normalizedScore
      });

      return normalizedScore;
    } catch (error) {
      logger.error('Failed to calculate lead score', { error, leadId: lead.id });
      // 返回默认评分
      return 50;
    }
  }

  // 基于规则的评分
  private calculateRuleBasedScore(lead: Partial<Lead>): number {
    let totalScore = 0;
    let totalWeight = 0;

    for (const rule of this.scoringConfig.rules) {
      try {
        const score = rule.calculate(lead);
        totalScore += score * rule.weight;
        totalWeight += rule.weight;
      } catch (error) {
        logger.warn(`Failed to apply scoring rule: ${rule.name}`, { error, leadId: lead.id });
      }
    }

    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
  }

  // AI评分
  private async calculateAIScore(lead: Partial<Lead>): Promise<number> {
    try {
      if (!this.openai) return 0;

      const prompt = this.buildAIScoringPrompt(lead);
      
      const response = await this.openai.chat.completions.create({
        model: config.ai.openai.model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert sales lead scoring AI. Analyze the lead information and provide a score from 0-100 based on the likelihood of conversion. Consider factors like company size, industry, job title, budget, timeline, and engagement level. Respond with only a number between 0 and 100.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 10,
        temperature: 0.1
      });

      const scoreText = response.choices[0]?.message?.content?.trim();
      const score = parseInt(scoreText || '0');

      return isNaN(score) ? 0 : Math.max(0, Math.min(100, score));
    } catch (error) {
      logger.error('Failed to calculate AI score', { error, leadId: lead.id });
      return 0;
    }
  }

  // 构建AI评分提示
  private buildAIScoringPrompt(lead: Partial<Lead>): string {
    const parts = [];

    if (lead.title) parts.push(`Title: ${lead.title}`);
    if (lead.description) parts.push(`Description: ${lead.description}`);
    if (lead.contactInfo?.email) parts.push(`Email: ${lead.contactInfo.email}`);
    if (lead.industry) parts.push(`Industry: ${lead.industry}`);
    if (lead.companySize) parts.push(`Company Size: ${lead.companySize}`);
    if (lead.jobTitle) parts.push(`Job Title: ${lead.jobTitle}`);
    if (lead.department) parts.push(`Department: ${lead.department}`);
    if (lead.budget) parts.push(`Budget: ${lead.budget}`);
    if (lead.timeline) parts.push(`Timeline: ${lead.timeline}`);
    if (lead.painPoints?.length) parts.push(`Pain Points: ${lead.painPoints.join(', ')}`);
    if (lead.interests?.length) parts.push(`Interests: ${lead.interests.join(', ')}`);
    if (lead.source) parts.push(`Source: ${lead.source}`);
    if (lead.touchCount) parts.push(`Touch Count: ${lead.touchCount}`);
    if (lead.engagementScore) parts.push(`Engagement Score: ${lead.engagementScore}`);

    return parts.join('\n');
  }

  // 初始化评分规则
  private initializeScoringRules(): ScoringRule[] {
    return [
      // 联系信息完整度 (权重: 15%)
      {
        name: 'contact_completeness',
        weight: 0.15,
        calculate: (lead) => {
          let score = 0;
          const contact = lead.contactInfo || {};
          
          if (contact.email) score += 40;
          if (contact.phone || contact.mobile) score += 30;
          if (lead.jobTitle) score += 15;
          if (lead.industry) score += 15;
          
          return score;
        }
      },

      // 公司规模评分 (权重: 20%)
      {
        name: 'company_size',
        weight: 0.20,
        calculate: (lead) => {
          const sizeScores: Record<string, number> = {
            'enterprise': 100,
            'large': 80,
            'medium': 60,
            'small': 40,
            'startup': 20
          };
          return sizeScores[lead.companySize || ''] || 30;
        }
      },

      // 职位级别评分 (权重: 18%)
      {
        name: 'job_title',
        weight: 0.18,
        calculate: (lead) => {
          const title = (lead.jobTitle || '').toLowerCase();
          
          // C级高管
          if (/\b(ceo|cto|cfo|coo|chief|president)\b/.test(title)) return 100;
          // VP/总监级
          if (/\b(vp|vice president|director|总监|副总)\b/.test(title)) return 85;
          // 经理级
          if (/\b(manager|head|lead|经理|主管)\b/.test(title)) return 70;
          // 专员级
          if (/\b(specialist|analyst|coordinator|专员|分析师)\b/.test(title)) return 50;
          // 其他
          return 30;
        }
      },

      // 决策者评分 (权重: 15%)
      {
        name: 'decision_maker',
        weight: 0.15,
        calculate: (lead) => {
          if (lead.decisionMaker === true) return 100;
          if (lead.decisionMaker === false) return 30;
          
          // 基于职位推断
          const title = (lead.jobTitle || '').toLowerCase();
          if (/\b(ceo|cto|cfo|coo|chief|president|vp|vice president|director)\b/.test(title)) {
            return 90;
          }
          if (/\b(manager|head|lead)\b/.test(title)) {
            return 70;
          }
          return 50;
        }
      },

      // 预算评分 (权重: 12%)
      {
        name: 'budget',
        weight: 0.12,
        calculate: (lead) => {
          if (!lead.budget) return 40;
          
          if (lead.budget >= 1000000) return 100; // 100万+
          if (lead.budget >= 500000) return 85;   // 50万+
          if (lead.budget >= 100000) return 70;   // 10万+
          if (lead.budget >= 50000) return 55;    // 5万+
          if (lead.budget >= 10000) return 40;    // 1万+
          return 25;
        }
      },

      // 时间线评分 (权重: 10%)
      {
        name: 'timeline',
        weight: 0.10,
        calculate: (lead) => {
          const timeline = (lead.timeline || '').toLowerCase();
          
          if (/\b(immediate|urgent|asap|立即|紧急)\b/.test(timeline)) return 100;
          if (/\b(1 month|一个月|本月)\b/.test(timeline)) return 85;
          if (/\b(3 month|三个月|季度)\b/.test(timeline)) return 70;
          if (/\b(6 month|半年)\b/.test(timeline)) return 55;
          if (/\b(1 year|一年|年内)\b/.test(timeline)) return 40;
          if (/\b(no timeline|不确定|未定)\b/.test(timeline)) return 20;
          
          return 50;
        }
      },

      // 参与度评分 (权重: 10%)
      {
        name: 'engagement',
        weight: 0.10,
        calculate: (lead) => {
          if (lead.engagementScore) return lead.engagementScore;
          
          // 基于接触次数计算
          const touchCount = lead.touchCount || 0;
          if (touchCount >= 10) return 100;
          if (touchCount >= 5) return 80;
          if (touchCount >= 3) return 60;
          if (touchCount >= 1) return 40;
          return 20;
        }
      }
    ];
  }

  // 批量重新计算评分
  async recalculateScores(leadIds?: string[]): Promise<void> {
    try {
      logger.info('Starting batch score recalculation', { leadIds });

      let query = Lead.query();
      if (leadIds) {
        query = query.findByIds(leadIds);
      }

      const leads = await query;
      const batchSize = 50;

      for (let i = 0; i < leads.length; i += batchSize) {
        const batch = leads.slice(i, i + batchSize);
        
        await Promise.all(batch.map(async (lead) => {
          try {
            const newScore = await this.calculateScore(lead);
            await lead.$query().patch({ score: newScore });
            
            logger.debug('Score updated', { 
              leadId: lead.id, 
              oldScore: lead.score, 
              newScore 
            });
          } catch (error) {
            logger.error('Failed to update score for lead', { 
              error, 
              leadId: lead.id 
            });
          }
        }));

        // 避免过于频繁的API调用
        if (this.scoringConfig.aiEnabled && i + batchSize < leads.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      logger.info('Batch score recalculation completed', { 
        total: leads.length 
      });
    } catch (error) {
      logger.error('Failed to recalculate scores', { error, leadIds });
      throw error;
    }
  }

  // 获取评分分布统计
  async getScoreDistribution(): Promise<any> {
    try {
      const distribution = await Lead.query()
        .select(
          Lead.raw(`
            CASE 
              WHEN score >= 80 THEN 'hot'
              WHEN score >= 60 THEN 'warm'
              WHEN score >= 40 THEN 'cold'
              ELSE 'very_cold'
            END as category
          `),
          Lead.raw('COUNT(*) as count')
        )
        .groupBy('category');

      const result = {
        hot: 0,      // 80-100
        warm: 0,     // 60-79
        cold: 0,     // 40-59
        very_cold: 0 // 0-39
      };

      distribution.forEach(item => {
        result[item.category as keyof typeof result] = parseInt(item.count);
      });

      return result;
    } catch (error) {
      logger.error('Failed to get score distribution', { error });
      throw error;
    }
  }

  // 获取评分规则配置
  getScoringRules(): ScoringRule[] {
    return this.scoringConfig.rules;
  }

  // 更新评分规则权重
  updateRuleWeights(weights: Record<string, number>): void {
    this.scoringConfig.rules.forEach(rule => {
      if (weights[rule.name] !== undefined) {
        rule.weight = weights[rule.name];
      }
    });

    logger.info('Scoring rule weights updated', { weights });
  }

  // 启用/禁用AI评分
  setAIEnabled(enabled: boolean): void {
    this.scoringConfig.aiEnabled = enabled && !!this.openai;
    logger.info('AI scoring enabled status changed', { enabled: this.scoringConfig.aiEnabled });
  }

  // 设置AI评分权重
  setAIWeight(weight: number): void {
    this.scoringConfig.aiWeight = Math.max(0, Math.min(1, weight));
    logger.info('AI scoring weight updated', { weight: this.scoringConfig.aiWeight });
  }
}
