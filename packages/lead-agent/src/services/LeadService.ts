import { Lead } from '../models/Lead';
import { LeadScoringService } from './LeadScoringService';
import { LeadAssignmentService } from './LeadAssignmentService';
import { LeadEnrichmentService } from './LeadEnrichmentService';
import { LeadDeduplicationService } from './LeadDeduplicationService';
import { NotificationService } from './NotificationService';
import { EventBus } from '../utils/event-bus';
import { logger } from '../utils/logger';
import { 
  Lead as ILead, 
  LeadSource, 
  LeadStatus, 
  Priority, 
  ContactInfo,
  QueryParams,
  PaginatedResult 
} from '@linkagent/shared/types';
import { createError } from '../middleware/error-handler';

export class LeadService {
  private scoringService: LeadScoringService;
  private assignmentService: LeadAssignmentService;
  private enrichmentService: LeadEnrichmentService;
  private deduplicationService: LeadDeduplicationService;
  private notificationService: NotificationService;
  private eventBus: EventBus;

  constructor() {
    this.scoringService = new LeadScoringService();
    this.assignmentService = new LeadAssignmentService();
    this.enrichmentService = new LeadEnrichmentService();
    this.deduplicationService = new LeadDeduplicationService();
    this.notificationService = new NotificationService();
    this.eventBus = EventBus.getInstance();
  }

  // 创建线索
  async create(leadData: Partial<ILead>, userId?: string): Promise<Lead> {
    try {
      logger.info('Creating new lead', { leadData, userId });

      // 数据验证和清洗
      const cleanedData = await this.validateAndCleanLeadData(leadData);

      // 去重检查
      const duplicates = await this.deduplicationService.findDuplicates(cleanedData);
      if (duplicates.length > 0) {
        logger.warn('Duplicate lead detected', { duplicates, leadData });
        // 可以选择合并或返回现有线索
        return await this.handleDuplicate(duplicates[0], cleanedData);
      }

      // 数据丰富化
      const enrichedData = await this.enrichmentService.enrichLead(cleanedData);

      // 计算初始评分
      const score = await this.scoringService.calculateScore(enrichedData);
      enrichedData.score = score;

      // 创建线索
      const lead = await Lead.query().insert({
        ...enrichedData,
        status: enrichedData.status || 'new',
        priority: enrichedData.priority || 'medium',
        tags: enrichedData.tags || [],
        metadata: enrichedData.metadata || {},
        firstTouchAt: new Date(),
        lastTouchAt: new Date(),
        touchCount: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // 自动分配
      if (!lead.assignedTo) {
        const assignedUser = await this.assignmentService.autoAssign(lead);
        if (assignedUser) {
          await lead.assign(assignedUser.id);
          logger.info('Lead auto-assigned', { leadId: lead.id, assignedTo: assignedUser.id });
        }
      }

      // 发送事件
      await this.eventBus.publish({
        type: 'lead.created',
        aggregateId: lead.id,
        data: { lead, createdBy: userId },
        timestamp: new Date()
      });

      // 发送通知
      if (lead.assignedTo) {
        await this.notificationService.notifyNewLead(lead.assignedTo, lead);
      }

      logger.info('Lead created successfully', { leadId: lead.id });
      return lead;
    } catch (error) {
      logger.error('Failed to create lead', { error, leadData });
      throw error;
    }
  }

  // 获取线索列表
  async findAll(params: QueryParams): Promise<PaginatedResult<Lead>> {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'created_at',
        sortOrder = 'desc',
        search,
        filters = {}
      } = params;

      let query = Lead.query()
        .withGraphFetched('[assignedUser, customer, campaign]');

      // 搜索
      if (search) {
        query = query.where(builder => {
          builder
            .where('title', 'ilike', `%${search}%`)
            .orWhere('description', 'ilike', `%${search}%`)
            .orWhere('contact_info:email', 'ilike', `%${search}%`)
            .orWhere('contact_info:phone', 'ilike', `%${search}%`);
        });
      }

      // 过滤器
      if (filters.status) {
        query = query.where('status', filters.status);
      }
      if (filters.source) {
        query = query.where('source', filters.source);
      }
      if (filters.priority) {
        query = query.where('priority', filters.priority);
      }
      if (filters.assignedTo) {
        query = query.where('assigned_to', filters.assignedTo);
      }
      if (filters.campaignId) {
        query = query.where('campaign_id', filters.campaignId);
      }
      if (filters.scoreMin) {
        query = query.where('score', '>=', filters.scoreMin);
      }
      if (filters.scoreMax) {
        query = query.where('score', '<=', filters.scoreMax);
      }
      if (filters.createdAfter) {
        query = query.where('created_at', '>=', filters.createdAfter);
      }
      if (filters.createdBefore) {
        query = query.where('created_at', '<=', filters.createdBefore);
      }

      // 排序
      query = query.orderBy(sortBy, sortOrder);

      // 分页
      const result = await query.page(page - 1, limit);

      return {
        data: result.results,
        total: result.total,
        page,
        limit,
        totalPages: Math.ceil(result.total / limit),
        hasNext: page < Math.ceil(result.total / limit),
        hasPrev: page > 1
      };
    } catch (error) {
      logger.error('Failed to fetch leads', { error, params });
      throw error;
    }
  }

  // 获取单个线索
  async findById(id: string): Promise<Lead> {
    try {
      const lead = await Lead.query()
        .findById(id)
        .withGraphFetched('[assignedUser, customer, campaign, activities, notes, followups]');

      if (!lead) {
        throw createError.notFound('Lead', id);
      }

      return lead;
    } catch (error) {
      logger.error('Failed to fetch lead', { error, id });
      throw error;
    }
  }

  // 更新线索
  async update(id: string, updateData: Partial<ILead>, userId?: string): Promise<Lead> {
    try {
      logger.info('Updating lead', { id, updateData, userId });

      const lead = await this.findById(id);
      const oldData = { ...lead };

      // 数据验证和清洗
      const cleanedData = await this.validateAndCleanLeadData(updateData);

      // 如果联系信息发生变化，检查重复
      if (cleanedData.contactInfo) {
        const duplicates = await this.deduplicationService.findDuplicates({
          ...lead,
          ...cleanedData
        });
        const otherDuplicates = duplicates.filter(dup => dup.id !== id);
        if (otherDuplicates.length > 0) {
          logger.warn('Duplicate lead detected during update', { duplicates: otherDuplicates, id });
        }
      }

      // 数据丰富化（仅对新增或修改的字段）
      const enrichedData = await this.enrichmentService.enrichLead({
        ...lead,
        ...cleanedData
      });

      // 重新计算评分（如果相关字段发生变化）
      if (this.shouldRecalculateScore(oldData, enrichedData)) {
        enrichedData.score = await this.scoringService.calculateScore(enrichedData);
      }

      // 更新线索
      const updatedLead = await lead.$query().patchAndFetch({
        ...enrichedData,
        updatedAt: new Date()
      });

      // 记录活动
      await updatedLead.addActivity('updated', 'Lead information updated', {
        changes: this.getChanges(oldData, enrichedData),
        updatedBy: userId
      });

      // 发送事件
      await this.eventBus.publish({
        type: 'lead.updated',
        aggregateId: id,
        data: { 
          lead: updatedLead, 
          oldData, 
          changes: this.getChanges(oldData, enrichedData),
          updatedBy: userId 
        },
        timestamp: new Date()
      });

      logger.info('Lead updated successfully', { leadId: id });
      return updatedLead;
    } catch (error) {
      logger.error('Failed to update lead', { error, id, updateData });
      throw error;
    }
  }

  // 删除线索
  async delete(id: string, userId?: string): Promise<void> {
    try {
      logger.info('Deleting lead', { id, userId });

      const lead = await this.findById(id);

      // 软删除
      await lead.$query().patch({
        deletedAt: new Date(),
        updatedAt: new Date()
      });

      // 发送事件
      await this.eventBus.publish({
        type: 'lead.deleted',
        aggregateId: id,
        data: { lead, deletedBy: userId },
        timestamp: new Date()
      });

      logger.info('Lead deleted successfully', { leadId: id });
    } catch (error) {
      logger.error('Failed to delete lead', { error, id });
      throw error;
    }
  }

  // 分配线索
  async assign(id: string, assigneeId: string, userId?: string): Promise<Lead> {
    try {
      logger.info('Assigning lead', { id, assigneeId, userId });

      const lead = await this.findById(id);
      await lead.assign(assigneeId);

      // 发送通知
      await this.notificationService.notifyLeadAssignment(assigneeId, lead);

      // 发送事件
      await this.eventBus.publish({
        type: 'lead.assigned',
        aggregateId: id,
        data: { lead, assigneeId, assignedBy: userId },
        timestamp: new Date()
      });

      logger.info('Lead assigned successfully', { leadId: id, assigneeId });
      return await this.findById(id);
    } catch (error) {
      logger.error('Failed to assign lead', { error, id, assigneeId });
      throw error;
    }
  }

  // 转换线索为商机
  async convert(id: string, opportunityData: any, userId?: string): Promise<Lead> {
    try {
      logger.info('Converting lead to opportunity', { id, opportunityData, userId });

      const lead = await this.findById(id);

      if (lead.status === 'converted') {
        throw createError.business('Lead already converted', 'LEAD_ALREADY_CONVERTED');
      }

      // 创建商机（这里需要调用商机服务）
      // const opportunity = await opportunityService.createFromLead(lead, opportunityData);
      const opportunityId = 'temp-opportunity-id'; // 临时ID

      // 转换线索
      await lead.convert(opportunityId);

      // 发送事件
      await this.eventBus.publish({
        type: 'lead.converted',
        aggregateId: id,
        data: { lead, opportunityId, convertedBy: userId },
        timestamp: new Date()
      });

      // 发送通知
      if (lead.assignedTo) {
        await this.notificationService.notifyLeadConversion(lead.assignedTo, lead, opportunityId);
      }

      logger.info('Lead converted successfully', { leadId: id, opportunityId });
      return await this.findById(id);
    } catch (error) {
      logger.error('Failed to convert lead', { error, id, opportunityData });
      throw error;
    }
  }

  // 批量操作
  async bulkUpdate(ids: string[], updateData: Partial<ILead>, userId?: string): Promise<Lead[]> {
    try {
      logger.info('Bulk updating leads', { ids, updateData, userId });

      const leads = await Lead.query().findByIds(ids);
      const updatedLeads: Lead[] = [];

      for (const lead of leads) {
        const updated = await this.update(lead.id, updateData, userId);
        updatedLeads.push(updated);
      }

      logger.info('Bulk update completed', { count: updatedLeads.length });
      return updatedLeads;
    } catch (error) {
      logger.error('Failed to bulk update leads', { error, ids, updateData });
      throw error;
    }
  }

  // 批量分配
  async bulkAssign(ids: string[], assigneeId: string, userId?: string): Promise<Lead[]> {
    try {
      logger.info('Bulk assigning leads', { ids, assigneeId, userId });

      const leads = await Lead.query().findByIds(ids);
      const assignedLeads: Lead[] = [];

      for (const lead of leads) {
        const assigned = await this.assign(lead.id, assigneeId, userId);
        assignedLeads.push(assigned);
      }

      logger.info('Bulk assignment completed', { count: assignedLeads.length });
      return assignedLeads;
    } catch (error) {
      logger.error('Failed to bulk assign leads', { error, ids, assigneeId });
      throw error;
    }
  }

  // 获取线索统计
  async getStatistics(filters?: any): Promise<any> {
    try {
      const baseQuery = Lead.query();

      // 应用过滤器
      if (filters?.assignedTo) {
        baseQuery.where('assigned_to', filters.assignedTo);
      }
      if (filters?.dateFrom) {
        baseQuery.where('created_at', '>=', filters.dateFrom);
      }
      if (filters?.dateTo) {
        baseQuery.where('created_at', '<=', filters.dateTo);
      }

      const [
        totalCount,
        statusStats,
        sourceStats,
        priorityStats,
        scoreStats
      ] = await Promise.all([
        baseQuery.clone().resultSize(),
        baseQuery.clone().groupBy('status').count('* as count').select('status'),
        baseQuery.clone().groupBy('source').count('* as count').select('source'),
        baseQuery.clone().groupBy('priority').count('* as count').select('priority'),
        baseQuery.clone().avg('score as avgScore').min('score as minScore').max('score as maxScore').first()
      ]);

      return {
        total: totalCount,
        byStatus: statusStats.reduce((acc, item) => {
          acc[item.status] = parseInt(item.count);
          return acc;
        }, {}),
        bySource: sourceStats.reduce((acc, item) => {
          acc[item.source] = parseInt(item.count);
          return acc;
        }, {}),
        byPriority: priorityStats.reduce((acc, item) => {
          acc[item.priority] = parseInt(item.count);
          return acc;
        }, {}),
        scoreStats: {
          average: Math.round(scoreStats?.avgScore || 0),
          minimum: scoreStats?.minScore || 0,
          maximum: scoreStats?.maxScore || 0
        }
      };
    } catch (error) {
      logger.error('Failed to get lead statistics', { error, filters });
      throw error;
    }
  }

  // 私有方法
  private async validateAndCleanLeadData(data: Partial<ILead>): Promise<Partial<ILead>> {
    // 数据验证和清洗逻辑
    const cleaned = { ...data };

    // 清理联系信息
    if (cleaned.contactInfo) {
      if (cleaned.contactInfo.email) {
        cleaned.contactInfo.email = cleaned.contactInfo.email.toLowerCase().trim();
      }
      if (cleaned.contactInfo.phone) {
        cleaned.contactInfo.phone = cleaned.contactInfo.phone.replace(/\D/g, '');
      }
    }

    // 清理标签
    if (cleaned.tags) {
      cleaned.tags = cleaned.tags.map(tag => tag.trim().toLowerCase()).filter(Boolean);
    }

    return cleaned;
  }

  private async handleDuplicate(existingLead: Lead, newData: Partial<ILead>): Promise<Lead> {
    // 合并重复线索的逻辑
    logger.info('Handling duplicate lead', { existingId: existingLead.id, newData });

    // 更新现有线索的信息
    const mergedData = {
      ...newData,
      touchCount: (existingLead.touchCount || 0) + 1,
      lastTouchAt: new Date()
    };

    return await this.update(existingLead.id, mergedData);
  }

  private shouldRecalculateScore(oldData: any, newData: any): boolean {
    const scoreFields = [
      'contactInfo', 'industry', 'companySize', 'jobTitle', 
      'budget', 'timeline', 'painPoints', 'interests'
    ];
    
    return scoreFields.some(field => 
      JSON.stringify(oldData[field]) !== JSON.stringify(newData[field])
    );
  }

  private getChanges(oldData: any, newData: any): Record<string, { old: any; new: any }> {
    const changes: Record<string, { old: any; new: any }> = {};
    
    Object.keys(newData).forEach(key => {
      if (JSON.stringify(oldData[key]) !== JSON.stringify(newData[key])) {
        changes[key] = {
          old: oldData[key],
          new: newData[key]
        };
      }
    });
    
    return changes;
  }
}
