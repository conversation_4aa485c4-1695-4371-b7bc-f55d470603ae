import { Model } from 'objection';
import { BaseModel } from '@linkagent/shared/models/BaseModel';
import { Lead as ILead, LeadSource, LeadStatus, Priority, ContactInfo } from '@linkagent/shared/types';

export class Lead extends BaseModel implements ILead {
  static tableName = 'leads';

  // 基础字段
  id!: string;
  customerId?: string;
  source!: LeadSource;
  title!: string;
  description?: string;
  status!: LeadStatus;
  priority!: Priority;
  score!: number;
  contactInfo!: ContactInfo;
  assignedTo?: string;
  tags!: string[];
  metadata!: Record<string, any>;
  convertedAt?: Date;
  convertedTo?: string;
  createdAt!: Date;
  updatedAt!: Date;

  // 扩展字段
  sourceDetails?: Record<string, any>; // 来源详细信息
  campaignId?: string; // 营销活动ID
  referralSource?: string; // 推荐来源
  landingPage?: string; // 着陆页
  utmSource?: string; // UTM来源
  utmMedium?: string; // UTM媒介
  utmCampaign?: string; // UTM活动
  utmTerm?: string; // UTM关键词
  utmContent?: string; // UTM内容
  ipAddress?: string; // IP地址
  userAgent?: string; // 用户代理
  deviceInfo?: Record<string, any>; // 设备信息
  geoLocation?: Record<string, any>; // 地理位置
  firstTouchAt?: Date; // 首次接触时间
  lastTouchAt?: Date; // 最后接触时间
  touchCount?: number; // 接触次数
  engagementScore?: number; // 参与度评分
  qualificationScore?: number; // 资格评分
  intentScore?: number; // 意向评分
  fitScore?: number; // 匹配度评分
  lastActivityAt?: Date; // 最后活动时间
  nextFollowupAt?: Date; // 下次跟进时间
  estimatedValue?: number; // 预估价值
  currency?: string; // 货币
  industry?: string; // 行业
  companySize?: string; // 公司规模
  jobTitle?: string; // 职位
  department?: string; // 部门
  decisionMaker?: boolean; // 是否决策者
  budget?: number; // 预算
  timeline?: string; // 时间线
  painPoints?: string[]; // 痛点
  interests?: string[]; // 兴趣点
  competitors?: string[]; // 竞争对手
  previousVendors?: string[]; // 之前的供应商

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['source', 'title', 'status', 'priority', 'score', 'contactInfo'],
      properties: {
        id: { type: 'string', format: 'uuid' },
        customerId: { type: ['string', 'null'], format: 'uuid' },
        source: { 
          type: 'string', 
          enum: ['website', 'social_media', 'email', 'phone', 'referral', 'advertisement', 'event', 'partner', 'other'] 
        },
        title: { type: 'string', minLength: 1, maxLength: 255 },
        description: { type: ['string', 'null'], maxLength: 2000 },
        status: { 
          type: 'string', 
          enum: ['new', 'contacted', 'qualified', 'unqualified', 'converted', 'lost'] 
        },
        priority: { 
          type: 'string', 
          enum: ['low', 'medium', 'high', 'urgent'] 
        },
        score: { type: 'number', minimum: 0, maximum: 100 },
        contactInfo: {
          type: 'object',
          properties: {
            email: { type: ['string', 'null'], format: 'email' },
            phone: { type: ['string', 'null'] },
            mobile: { type: ['string', 'null'] },
            fax: { type: ['string', 'null'] },
            wechat: { type: ['string', 'null'] },
            qq: { type: ['string', 'null'] }
          }
        },
        assignedTo: { type: ['string', 'null'], format: 'uuid' },
        tags: { type: 'array', items: { type: 'string' } },
        metadata: { type: 'object' },
        convertedAt: { type: ['string', 'null'], format: 'date-time' },
        convertedTo: { type: ['string', 'null'], format: 'uuid' },
        sourceDetails: { type: ['object', 'null'] },
        campaignId: { type: ['string', 'null'], format: 'uuid' },
        referralSource: { type: ['string', 'null'] },
        landingPage: { type: ['string', 'null'] },
        utmSource: { type: ['string', 'null'] },
        utmMedium: { type: ['string', 'null'] },
        utmCampaign: { type: ['string', 'null'] },
        utmTerm: { type: ['string', 'null'] },
        utmContent: { type: ['string', 'null'] },
        ipAddress: { type: ['string', 'null'] },
        userAgent: { type: ['string', 'null'] },
        deviceInfo: { type: ['object', 'null'] },
        geoLocation: { type: ['object', 'null'] },
        firstTouchAt: { type: ['string', 'null'], format: 'date-time' },
        lastTouchAt: { type: ['string', 'null'], format: 'date-time' },
        touchCount: { type: ['number', 'null'], minimum: 0 },
        engagementScore: { type: ['number', 'null'], minimum: 0, maximum: 100 },
        qualificationScore: { type: ['number', 'null'], minimum: 0, maximum: 100 },
        intentScore: { type: ['number', 'null'], minimum: 0, maximum: 100 },
        fitScore: { type: ['number', 'null'], minimum: 0, maximum: 100 },
        lastActivityAt: { type: ['string', 'null'], format: 'date-time' },
        nextFollowupAt: { type: ['string', 'null'], format: 'date-time' },
        estimatedValue: { type: ['number', 'null'], minimum: 0 },
        currency: { type: ['string', 'null'], minLength: 3, maxLength: 3 },
        industry: { type: ['string', 'null'] },
        companySize: { type: ['string', 'null'] },
        jobTitle: { type: ['string', 'null'] },
        department: { type: ['string', 'null'] },
        decisionMaker: { type: ['boolean', 'null'] },
        budget: { type: ['number', 'null'], minimum: 0 },
        timeline: { type: ['string', 'null'] },
        painPoints: { type: ['array', 'null'], items: { type: 'string' } },
        interests: { type: ['array', 'null'], items: { type: 'string' } },
        competitors: { type: ['array', 'null'], items: { type: 'string' } },
        previousVendors: { type: ['array', 'null'], items: { type: 'string' } }
      }
    };
  }

  static get relationMappings() {
    return {
      customer: {
        relation: Model.BelongsToOneRelation,
        modelClass: 'Customer',
        join: {
          from: 'leads.customer_id',
          to: 'customers.id'
        }
      },
      assignedUser: {
        relation: Model.BelongsToOneRelation,
        modelClass: 'User',
        join: {
          from: 'leads.assigned_to',
          to: 'users.id'
        }
      },
      campaign: {
        relation: Model.BelongsToOneRelation,
        modelClass: 'Campaign',
        join: {
          from: 'leads.campaign_id',
          to: 'campaigns.id'
        }
      },
      activities: {
        relation: Model.HasManyRelation,
        modelClass: 'LeadActivity',
        join: {
          from: 'leads.id',
          to: 'lead_activities.lead_id'
        }
      },
      notes: {
        relation: Model.HasManyRelation,
        modelClass: 'LeadNote',
        join: {
          from: 'leads.id',
          to: 'lead_notes.lead_id'
        }
      },
      followups: {
        relation: Model.HasManyRelation,
        modelClass: 'LeadFollowup',
        join: {
          from: 'leads.id',
          to: 'lead_followups.lead_id'
        }
      },
      opportunity: {
        relation: Model.HasOneRelation,
        modelClass: 'Opportunity',
        join: {
          from: 'leads.converted_to',
          to: 'opportunities.id'
        }
      }
    };
  }

  // 虚拟属性
  get fullName(): string {
    const { email, phone } = this.contactInfo;
    return email || phone || 'Unknown';
  }

  get isConverted(): boolean {
    return this.status === 'converted' && !!this.convertedAt;
  }

  get isQualified(): boolean {
    return ['qualified', 'converted'].includes(this.status);
  }

  get daysSinceCreated(): number {
    return Math.floor((Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60 * 24));
  }

  get daysSinceLastActivity(): number {
    if (!this.lastActivityAt) return this.daysSinceCreated;
    return Math.floor((Date.now() - this.lastActivityAt.getTime()) / (1000 * 60 * 60 * 24));
  }

  get isOverdue(): boolean {
    if (!this.nextFollowupAt) return false;
    return new Date() > this.nextFollowupAt;
  }

  // 实例方法
  async updateScore(): Promise<void> {
    // 计算综合评分
    const scores = [
      this.engagementScore || 0,
      this.qualificationScore || 0,
      this.intentScore || 0,
      this.fitScore || 0
    ].filter(score => score > 0);

    if (scores.length > 0) {
      this.score = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
      await this.$query().patch({ score: this.score });
    }
  }

  async addActivity(type: string, description: string, metadata?: Record<string, any>): Promise<void> {
    await this.$relatedQuery('activities').insert({
      type,
      description,
      metadata: metadata || {},
      createdAt: new Date()
    });

    // 更新最后活动时间
    this.lastActivityAt = new Date();
    this.touchCount = (this.touchCount || 0) + 1;
    await this.$query().patch({
      lastActivityAt: this.lastActivityAt,
      touchCount: this.touchCount
    });
  }

  async addNote(content: string, isPrivate: boolean = false): Promise<void> {
    await this.$relatedQuery('notes').insert({
      content,
      isPrivate,
      createdAt: new Date()
    });
  }

  async scheduleFollowup(scheduledAt: Date, type: string, description?: string): Promise<void> {
    await this.$relatedQuery('followups').insert({
      scheduledAt,
      type,
      description,
      status: 'pending',
      createdAt: new Date()
    });

    // 更新下次跟进时间
    this.nextFollowupAt = scheduledAt;
    await this.$query().patch({ nextFollowupAt: scheduledAt });
  }

  async convert(opportunityId: string): Promise<void> {
    this.status = 'converted';
    this.convertedAt = new Date();
    this.convertedTo = opportunityId;

    await this.$query().patch({
      status: this.status,
      convertedAt: this.convertedAt,
      convertedTo: this.convertedTo
    });

    // 记录转换活动
    await this.addActivity('converted', `Lead converted to opportunity ${opportunityId}`, {
      opportunityId
    });
  }

  async qualify(): Promise<void> {
    this.status = 'qualified';
    await this.$query().patch({ status: this.status });
    await this.addActivity('qualified', 'Lead qualified');
  }

  async disqualify(reason?: string): Promise<void> {
    this.status = 'unqualified';
    await this.$query().patch({ status: this.status });
    await this.addActivity('disqualified', `Lead disqualified: ${reason || 'No reason provided'}`, {
      reason
    });
  }

  async assign(userId: string): Promise<void> {
    this.assignedTo = userId;
    await this.$query().patch({ assignedTo: userId });
    await this.addActivity('assigned', `Lead assigned to user ${userId}`, {
      assignedTo: userId
    });
  }

  // 静态方法
  static async findByEmail(email: string): Promise<Lead[]> {
    return this.query()
      .where('contact_info:email', email)
      .orderBy('created_at', 'desc');
  }

  static async findByPhone(phone: string): Promise<Lead[]> {
    return this.query()
      .where('contact_info:phone', phone)
      .orWhere('contact_info:mobile', phone)
      .orderBy('created_at', 'desc');
  }

  static async findBySource(source: LeadSource): Promise<Lead[]> {
    return this.query()
      .where('source', source)
      .orderBy('created_at', 'desc');
  }

  static async findByStatus(status: LeadStatus): Promise<Lead[]> {
    return this.query()
      .where('status', status)
      .orderBy('created_at', 'desc');
  }

  static async findByAssignee(userId: string): Promise<Lead[]> {
    return this.query()
      .where('assigned_to', userId)
      .orderBy('created_at', 'desc');
  }

  static async findHighPriority(): Promise<Lead[]> {
    return this.query()
      .whereIn('priority', ['high', 'urgent'])
      .where('status', '!=', 'converted')
      .orderBy('priority', 'desc')
      .orderBy('created_at', 'desc');
  }

  static async findOverdue(): Promise<Lead[]> {
    return this.query()
      .where('next_followup_at', '<', new Date())
      .where('status', 'not in', ['converted', 'lost', 'unqualified'])
      .orderBy('next_followup_at', 'asc');
  }

  static async findUnassigned(): Promise<Lead[]> {
    return this.query()
      .whereNull('assigned_to')
      .where('status', '!=', 'converted')
      .orderBy('score', 'desc')
      .orderBy('created_at', 'desc');
  }
}
