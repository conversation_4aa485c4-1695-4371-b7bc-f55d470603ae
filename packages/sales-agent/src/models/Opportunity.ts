import { Model } from 'objection';
import { BaseModel } from '@linkagent/shared/models/BaseModel';
import { Opportunity as IOpportunity, OpportunityStage, Priority } from '@linkagent/shared/types';

export class Opportunity extends BaseModel implements IOpportunity {
  static tableName = 'opportunities';

  // 基础字段
  id!: string;
  customerId!: string;
  leadId?: string;
  name!: string;
  description?: string;
  stage!: OpportunityStage;
  probability!: number;
  amount!: number;
  currency!: string;
  expectedCloseDate!: Date;
  actualCloseDate?: Date;
  assignedTo!: string;
  tags!: string[];
  metadata!: Record<string, any>;
  createdAt!: Date;
  updatedAt!: Date;

  // 扩展字段
  source?: string; // 来源
  competitorInfo?: Record<string, any>; // 竞争对手信息
  decisionCriteria?: string[]; // 决策标准
  stakeholders?: Array<{
    name: string;
    role: string;
    influence: 'high' | 'medium' | 'low';
    sentiment: 'positive' | 'neutral' | 'negative';
    contactInfo?: Record<string, any>;
  }>; // 利益相关者
  salesProcess?: {
    currentStep: string;
    completedSteps: string[];
    nextSteps: string[];
    timeline: Record<string, Date>;
  }; // 销售流程
  products?: Array<{
    productId: string;
    productName: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    discount?: number;
    notes?: string;
  }>; // 产品信息
  totalDiscount?: number; // 总折扣
  netAmount?: number; // 净金额
  forecastCategory?: 'commit' | 'best_case' | 'pipeline' | 'omitted'; // 预测分类
  lostReason?: string; // 丢失原因
  winReason?: string; // 赢得原因
  nextActivity?: {
    type: string;
    description: string;
    scheduledAt: Date;
    assignedTo: string;
  }; // 下次活动
  lastActivityAt?: Date; // 最后活动时间
  stageHistory?: Array<{
    stage: OpportunityStage;
    changedAt: Date;
    changedBy: string;
    duration?: number; // 在该阶段停留时间（天）
  }>; // 阶段历史
  communicationHistory?: Array<{
    type: 'email' | 'call' | 'meeting' | 'demo' | 'proposal' | 'other';
    subject: string;
    content?: string;
    participants: string[];
    scheduledAt?: Date;
    completedAt?: Date;
    outcome?: string;
    nextSteps?: string[];
  }>; // 沟通历史
  riskFactors?: Array<{
    type: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
    mitigation?: string;
    identifiedAt: Date;
  }>; // 风险因素
  successFactors?: Array<{
    type: string;
    description: string;
    impact: 'low' | 'medium' | 'high';
    identifiedAt: Date;
  }>; // 成功因素

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['customerId', 'name', 'stage', 'probability', 'amount', 'currency', 'expectedCloseDate', 'assignedTo'],
      properties: {
        id: { type: 'string', format: 'uuid' },
        customerId: { type: 'string', format: 'uuid' },
        leadId: { type: ['string', 'null'], format: 'uuid' },
        name: { type: 'string', minLength: 1, maxLength: 255 },
        description: { type: ['string', 'null'], maxLength: 2000 },
        stage: { 
          type: 'string', 
          enum: ['prospecting', 'qualification', 'proposal', 'negotiation', 'closed_won', 'closed_lost'] 
        },
        probability: { type: 'number', minimum: 0, maximum: 100 },
        amount: { type: 'number', minimum: 0 },
        currency: { type: 'string', minLength: 3, maxLength: 3 },
        expectedCloseDate: { type: 'string', format: 'date' },
        actualCloseDate: { type: ['string', 'null'], format: 'date' },
        assignedTo: { type: 'string', format: 'uuid' },
        tags: { type: 'array', items: { type: 'string' } },
        metadata: { type: 'object' },
        source: { type: ['string', 'null'] },
        competitorInfo: { type: ['object', 'null'] },
        decisionCriteria: { type: ['array', 'null'], items: { type: 'string' } },
        stakeholders: { type: ['array', 'null'] },
        salesProcess: { type: ['object', 'null'] },
        products: { type: ['array', 'null'] },
        totalDiscount: { type: ['number', 'null'], minimum: 0 },
        netAmount: { type: ['number', 'null'], minimum: 0 },
        forecastCategory: { 
          type: ['string', 'null'], 
          enum: ['commit', 'best_case', 'pipeline', 'omitted'] 
        },
        lostReason: { type: ['string', 'null'] },
        winReason: { type: ['string', 'null'] },
        nextActivity: { type: ['object', 'null'] },
        lastActivityAt: { type: ['string', 'null'], format: 'date-time' },
        stageHistory: { type: ['array', 'null'] },
        communicationHistory: { type: ['array', 'null'] },
        riskFactors: { type: ['array', 'null'] },
        successFactors: { type: ['array', 'null'] }
      }
    };
  }

  static get relationMappings() {
    return {
      customer: {
        relation: Model.BelongsToOneRelation,
        modelClass: 'Customer',
        join: {
          from: 'opportunities.customer_id',
          to: 'customers.id'
        }
      },
      lead: {
        relation: Model.BelongsToOneRelation,
        modelClass: 'Lead',
        join: {
          from: 'opportunities.lead_id',
          to: 'leads.id'
        }
      },
      assignedUser: {
        relation: Model.BelongsToOneRelation,
        modelClass: 'User',
        join: {
          from: 'opportunities.assigned_to',
          to: 'users.id'
        }
      },
      quotes: {
        relation: Model.HasManyRelation,
        modelClass: 'Quote',
        join: {
          from: 'opportunities.id',
          to: 'quotes.opportunity_id'
        }
      },
      contracts: {
        relation: Model.HasManyRelation,
        modelClass: 'Contract',
        join: {
          from: 'opportunities.id',
          to: 'contracts.opportunity_id'
        }
      },
      activities: {
        relation: Model.HasManyRelation,
        modelClass: 'OpportunityActivity',
        join: {
          from: 'opportunities.id',
          to: 'opportunity_activities.opportunity_id'
        }
      },
      notes: {
        relation: Model.HasManyRelation,
        modelClass: 'OpportunityNote',
        join: {
          from: 'opportunities.id',
          to: 'opportunity_notes.opportunity_id'
        }
      }
    };
  }

  // 虚拟属性
  get isWon(): boolean {
    return this.stage === 'closed_won';
  }

  get isLost(): boolean {
    return this.stage === 'closed_lost';
  }

  get isClosed(): boolean {
    return this.isWon || this.isLost;
  }

  get isActive(): boolean {
    return !this.isClosed;
  }

  get daysToClose(): number {
    if (this.isClosed) return 0;
    return Math.ceil((this.expectedCloseDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
  }

  get isOverdue(): boolean {
    return !this.isClosed && this.daysToClose < 0;
  }

  get salesCycle(): number {
    if (!this.isClosed || !this.actualCloseDate) return 0;
    return Math.ceil((this.actualCloseDate.getTime() - this.createdAt.getTime()) / (1000 * 60 * 60 * 24));
  }

  get weightedAmount(): number {
    return Math.round(this.amount * (this.probability / 100));
  }

  get currentStageAge(): number {
    const lastStageChange = this.stageHistory?.[this.stageHistory.length - 1];
    if (!lastStageChange) return this.daysSinceCreated;
    return Math.ceil((Date.now() - lastStageChange.changedAt.getTime()) / (1000 * 60 * 60 * 24));
  }

  get daysSinceCreated(): number {
    return Math.ceil((Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60 * 24));
  }

  get daysSinceLastActivity(): number {
    if (!this.lastActivityAt) return this.daysSinceCreated;
    return Math.ceil((Date.now() - this.lastActivityAt.getTime()) / (1000 * 60 * 60 * 24));
  }

  // 实例方法
  async moveToStage(newStage: OpportunityStage, userId: string, reason?: string): Promise<void> {
    const oldStage = this.stage;
    const now = new Date();

    // 更新阶段历史
    const stageHistory = this.stageHistory || [];
    
    // 计算在上一个阶段的停留时间
    if (stageHistory.length > 0) {
      const lastStage = stageHistory[stageHistory.length - 1];
      lastStage.duration = Math.ceil((now.getTime() - lastStage.changedAt.getTime()) / (1000 * 60 * 60 * 24));
    }

    // 添加新阶段记录
    stageHistory.push({
      stage: newStage,
      changedAt: now,
      changedBy: userId
    });

    // 更新概率（基于阶段的默认概率）
    const stageProbabilities: Record<OpportunityStage, number> = {
      prospecting: 10,
      qualification: 25,
      proposal: 50,
      negotiation: 75,
      closed_won: 100,
      closed_lost: 0
    };

    const newProbability = stageProbabilities[newStage];

    // 如果是关闭状态，设置实际关闭日期
    const actualCloseDate = (newStage === 'closed_won' || newStage === 'closed_lost') ? now : undefined;

    await this.$query().patch({
      stage: newStage,
      probability: newProbability,
      stageHistory,
      actualCloseDate,
      updatedAt: now
    });

    // 记录活动
    await this.addActivity('stage_changed', `Stage changed from ${oldStage} to ${newStage}`, {
      oldStage,
      newStage,
      reason,
      changedBy: userId
    });
  }

  async updateProbability(probability: number, userId: string): Promise<void> {
    await this.$query().patch({
      probability,
      updatedAt: new Date()
    });

    await this.addActivity('probability_updated', `Probability updated to ${probability}%`, {
      probability,
      updatedBy: userId
    });
  }

  async addStakeholder(stakeholder: any): Promise<void> {
    const stakeholders = this.stakeholders || [];
    stakeholders.push({
      ...stakeholder,
      addedAt: new Date()
    });

    await this.$query().patch({
      stakeholders,
      updatedAt: new Date()
    });
  }

  async addProduct(product: any): Promise<void> {
    const products = this.products || [];
    products.push(product);

    // 重新计算总金额
    const totalAmount = products.reduce((sum, p) => sum + (p.totalPrice || 0), 0);
    const totalDiscount = products.reduce((sum, p) => sum + (p.discount || 0), 0);
    const netAmount = totalAmount - totalDiscount;

    await this.$query().patch({
      products,
      amount: totalAmount,
      totalDiscount,
      netAmount,
      updatedAt: new Date()
    });
  }

  async addRiskFactor(risk: any): Promise<void> {
    const riskFactors = this.riskFactors || [];
    riskFactors.push({
      ...risk,
      identifiedAt: new Date()
    });

    await this.$query().patch({
      riskFactors,
      updatedAt: new Date()
    });
  }

  async addSuccessFactor(factor: any): Promise<void> {
    const successFactors = this.successFactors || [];
    successFactors.push({
      ...factor,
      identifiedAt: new Date()
    });

    await this.$query().patch({
      successFactors,
      updatedAt: new Date()
    });
  }

  async scheduleNextActivity(activity: any): Promise<void> {
    await this.$query().patch({
      nextActivity: {
        ...activity,
        scheduledAt: new Date(activity.scheduledAt)
      },
      updatedAt: new Date()
    });

    await this.addActivity('activity_scheduled', `Next activity scheduled: ${activity.type}`, {
      activity
    });
  }

  async addActivity(type: string, description: string, metadata?: Record<string, any>): Promise<void> {
    await this.$relatedQuery('activities').insert({
      type,
      description,
      metadata: metadata || {},
      createdAt: new Date()
    });

    // 更新最后活动时间
    this.lastActivityAt = new Date();
    await this.$query().patch({
      lastActivityAt: this.lastActivityAt
    });
  }

  async addNote(content: string, isPrivate: boolean = false): Promise<void> {
    await this.$relatedQuery('notes').insert({
      content,
      isPrivate,
      createdAt: new Date()
    });
  }

  async win(reason?: string, userId?: string): Promise<void> {
    this.winReason = reason;
    await this.moveToStage('closed_won', userId || 'system', reason);
  }

  async lose(reason?: string, userId?: string): Promise<void> {
    this.lostReason = reason;
    await this.moveToStage('closed_lost', userId || 'system', reason);
  }

  // 静态方法
  static async findByStage(stage: OpportunityStage): Promise<Opportunity[]> {
    return this.query()
      .where('stage', stage)
      .orderBy('expected_close_date', 'asc');
  }

  static async findByAssignee(userId: string): Promise<Opportunity[]> {
    return this.query()
      .where('assigned_to', userId)
      .where('stage', 'not in', ['closed_won', 'closed_lost'])
      .orderBy('expected_close_date', 'asc');
  }

  static async findOverdue(): Promise<Opportunity[]> {
    return this.query()
      .where('expected_close_date', '<', new Date())
      .where('stage', 'not in', ['closed_won', 'closed_lost'])
      .orderBy('expected_close_date', 'asc');
  }

  static async findClosingSoon(days: number = 30): Promise<Opportunity[]> {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    return this.query()
      .where('expected_close_date', '<=', futureDate)
      .where('stage', 'not in', ['closed_won', 'closed_lost'])
      .orderBy('expected_close_date', 'asc');
  }

  static async findStale(days: number = 30): Promise<Opportunity[]> {
    const staleDate = new Date();
    staleDate.setDate(staleDate.getDate() - days);

    return this.query()
      .where('last_activity_at', '<', staleDate)
      .where('stage', 'not in', ['closed_won', 'closed_lost'])
      .orderBy('last_activity_at', 'asc');
  }

  static async getWinRate(userId?: string, dateFrom?: Date, dateTo?: Date): Promise<number> {
    let query = this.query()
      .where('stage', 'in', ['closed_won', 'closed_lost']);

    if (userId) {
      query = query.where('assigned_to', userId);
    }

    if (dateFrom) {
      query = query.where('actual_close_date', '>=', dateFrom);
    }

    if (dateTo) {
      query = query.where('actual_close_date', '<=', dateTo);
    }

    const results = await query
      .groupBy('stage')
      .count('* as count')
      .select('stage');

    const won = results.find(r => r.stage === 'closed_won')?.count || 0;
    const lost = results.find(r => r.stage === 'closed_lost')?.count || 0;
    const total = won + lost;

    return total > 0 ? Math.round((won / total) * 100) : 0;
  }
}
