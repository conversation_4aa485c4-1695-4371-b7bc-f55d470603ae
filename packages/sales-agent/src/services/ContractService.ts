import { Contract } from '../models/Contract';
import { ContractTemplateService } from './ContractTemplateService';
import { ElectronicSignatureService } from './ElectronicSignatureService';
import { ContractAnalysisService } from './ContractAnalysisService';
import { NotificationService } from './NotificationService';
import { EventBus } from '../utils/event-bus';
import { logger } from '../utils/logger';
import { 
  Contract as IContract, 
  ContractStatus, 
  ContractType,
  QueryParams,
  PaginatedResult 
} from '@linkagent/shared/types';
import { createError } from '../middleware/error-handler';

export class ContractService {
  private templateService: ContractTemplateService;
  private signatureService: ElectronicSignatureService;
  private analysisService: ContractAnalysisService;
  private notificationService: NotificationService;
  private eventBus: EventBus;

  constructor() {
    this.templateService = new ContractTemplateService();
    this.signatureService = new ElectronicSignatureService();
    this.analysisService = new ContractAnalysisService();
    this.notificationService = new NotificationService();
    this.eventBus = EventBus.getInstance();
  }

  // 创建合同
  async create(contractData: Partial<IContract>, userId?: string): Promise<Contract> {
    try {
      logger.info('Creating new contract', { contractData, userId });

      // 数据验证和清洗
      const cleanedData = await this.validateAndCleanContractData(contractData);

      // 生成合同编号
      const contractNumber = await this.generateContractNumber(cleanedData.type || 'sales');

      // 设置默认值
      const defaultData = {
        contractNumber,
        status: 'draft' as ContractStatus,
        terms: [],
        attachments: [],
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // 创建合同
      const contract = await Contract.query().insert({
        ...defaultData,
        ...cleanedData
      });

      // 如果指定了模板，应用模板
      if (contractData.templateId) {
        await this.applyTemplate(contract, contractData.templateId);
      }

      // 合同条款分析
      if (contract.terms.length > 0) {
        await this.analysisService.analyzeTerms(contract);
      }

      // 发送事件
      await this.eventBus.publish({
        type: 'contract.created',
        aggregateId: contract.id,
        data: { contract, createdBy: userId },
        timestamp: new Date()
      });

      // 发送通知
      if (contract.assignedTo) {
        await this.notificationService.notifyNewContract(contract.assignedTo, contract);
      }

      logger.info('Contract created successfully', { contractId: contract.id });
      return contract;
    } catch (error) {
      logger.error('Failed to create contract', { error, contractData });
      throw error;
    }
  }

  // 从商机创建合同
  async createFromOpportunity(opportunity: any, contractData: Partial<IContract>, userId?: string): Promise<Contract> {
    try {
      logger.info('Creating contract from opportunity', { opportunityId: opportunity.id, contractData, userId });

      // 从商机数据映射到合同数据
      const mappedData = {
        opportunityId: opportunity.id,
        customerId: opportunity.customerId,
        title: contractData.title || `Contract for ${opportunity.name}`,
        description: contractData.description || opportunity.description,
        type: contractData.type || 'sales',
        amount: contractData.amount || opportunity.amount,
        currency: contractData.currency || opportunity.currency,
        startDate: contractData.startDate || new Date(),
        endDate: contractData.endDate || this.calculateDefaultEndDate(),
        assignedTo: contractData.assignedTo || opportunity.assignedTo,
        metadata: {
          ...contractData.metadata,
          createdFromOpportunity: {
            opportunityId: opportunity.id,
            opportunityName: opportunity.name,
            createdAt: new Date(),
            createdBy: userId
          }
        },
        // 从商机产品信息生成合同条款
        terms: this.generateTermsFromOpportunity(opportunity)
      };

      const contract = await this.create(mappedData, userId);

      logger.info('Contract created from opportunity successfully', { 
        contractId: contract.id, 
        opportunityId: opportunity.id 
      });

      return contract;
    } catch (error) {
      logger.error('Failed to create contract from opportunity', { error, opportunityId: opportunity.id });
      throw error;
    }
  }

  // 获取合同列表
  async findAll(params: QueryParams): Promise<PaginatedResult<Contract>> {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'created_at',
        sortOrder = 'desc',
        search,
        filters = {}
      } = params;

      let query = Contract.query()
        .withGraphFetched('[customer, assignedUser, opportunity]');

      // 搜索
      if (search) {
        query = query.where(builder => {
          builder
            .where('title', 'ilike', `%${search}%`)
            .orWhere('contract_number', 'ilike', `%${search}%`)
            .orWhere('description', 'ilike', `%${search}%`);
        });
      }

      // 过滤器
      if (filters.status) {
        query = query.where('status', filters.status);
      }
      if (filters.type) {
        query = query.where('type', filters.type);
      }
      if (filters.assignedTo) {
        query = query.where('assigned_to', filters.assignedTo);
      }
      if (filters.customerId) {
        query = query.where('customer_id', filters.customerId);
      }
      if (filters.opportunityId) {
        query = query.where('opportunity_id', filters.opportunityId);
      }
      if (filters.amountMin) {
        query = query.where('amount', '>=', filters.amountMin);
      }
      if (filters.amountMax) {
        query = query.where('amount', '<=', filters.amountMax);
      }
      if (filters.startDateAfter) {
        query = query.where('start_date', '>=', filters.startDateAfter);
      }
      if (filters.endDateBefore) {
        query = query.where('end_date', '<=', filters.endDateBefore);
      }
      if (filters.isExpiring) {
        const expiringDate = new Date();
        expiringDate.setDate(expiringDate.getDate() + (filters.expiringDays || 30));
        query = query.where('end_date', '<=', expiringDate)
          .where('status', 'active');
      }

      // 排序
      query = query.orderBy(sortBy, sortOrder);

      // 分页
      const result = await query.page(page - 1, limit);

      return {
        data: result.results,
        total: result.total,
        page,
        limit,
        totalPages: Math.ceil(result.total / limit),
        hasNext: page < Math.ceil(result.total / limit),
        hasPrev: page > 1
      };
    } catch (error) {
      logger.error('Failed to fetch contracts', { error, params });
      throw error;
    }
  }

  // 获取单个合同
  async findById(id: string): Promise<Contract> {
    try {
      const contract = await Contract.query()
        .findById(id)
        .withGraphFetched('[customer, assignedUser, opportunity]');

      if (!contract) {
        throw createError.notFound('Contract', id);
      }

      return contract;
    } catch (error) {
      logger.error('Failed to fetch contract', { error, id });
      throw error;
    }
  }

  // 更新合同
  async update(id: string, updateData: Partial<IContract>, userId?: string): Promise<Contract> {
    try {
      logger.info('Updating contract', { id, updateData, userId });

      const contract = await this.findById(id);
      const oldData = { ...contract };

      // 检查合同状态是否允许修改
      if (contract.status === 'signed' && !this.isAllowedUpdateForSignedContract(updateData)) {
        throw createError.business('Cannot modify signed contract', 'SIGNED_CONTRACT_MODIFICATION_NOT_ALLOWED');
      }

      // 数据验证和清洗
      const cleanedData = await this.validateAndCleanContractData(updateData);

      // 更新合同
      const updatedContract = await contract.$query().patchAndFetch({
        ...cleanedData,
        updatedAt: new Date()
      });

      // 记录活动
      await this.addContractActivity(updatedContract, 'updated', 'Contract information updated', {
        changes: this.getChanges(oldData, cleanedData),
        updatedBy: userId
      });

      // 如果条款发生变化，重新分析
      if (cleanedData.terms) {
        await this.analysisService.analyzeTerms(updatedContract);
      }

      // 发送事件
      await this.eventBus.publish({
        type: 'contract.updated',
        aggregateId: id,
        data: { 
          contract: updatedContract, 
          oldData, 
          changes: this.getChanges(oldData, cleanedData),
          updatedBy: userId 
        },
        timestamp: new Date()
      });

      logger.info('Contract updated successfully', { contractId: id });
      return updatedContract;
    } catch (error) {
      logger.error('Failed to update contract', { error, id, updateData });
      throw error;
    }
  }

  // 提交审批
  async submitForApproval(id: string, userId?: string): Promise<Contract> {
    try {
      const contract = await this.findById(id);

      if (contract.status !== 'draft') {
        throw createError.business('Only draft contracts can be submitted for approval', 'INVALID_CONTRACT_STATUS');
      }

      // 验证合同完整性
      await this.validateContractCompleteness(contract);

      // 更新状态
      const updatedContract = await contract.$query().patchAndFetch({
        status: 'pending_approval',
        updatedAt: new Date()
      });

      // 记录活动
      await this.addContractActivity(updatedContract, 'submitted_for_approval', 'Contract submitted for approval', {
        submittedBy: userId
      });

      // 发送审批通知
      await this.notificationService.notifyContractApprovalRequired(updatedContract);

      // 发送事件
      await this.eventBus.publish({
        type: 'contract.submitted_for_approval',
        aggregateId: id,
        data: { contract: updatedContract, submittedBy: userId },
        timestamp: new Date()
      });

      logger.info('Contract submitted for approval', { contractId: id });
      return updatedContract;
    } catch (error) {
      logger.error('Failed to submit contract for approval', { error, id });
      throw error;
    }
  }

  // 审批合同
  async approve(id: string, approverId: string, comments?: string): Promise<Contract> {
    try {
      const contract = await this.findById(id);

      if (contract.status !== 'pending_approval') {
        throw createError.business('Only pending contracts can be approved', 'INVALID_CONTRACT_STATUS');
      }

      // 更新状态
      const updatedContract = await contract.$query().patchAndFetch({
        status: 'approved',
        approvedBy: approverId,
        approvedAt: new Date(),
        updatedAt: new Date()
      });

      // 记录活动
      await this.addContractActivity(updatedContract, 'approved', 'Contract approved', {
        approvedBy: approverId,
        comments
      });

      // 发送通知
      if (contract.assignedTo) {
        await this.notificationService.notifyContractApproved(contract.assignedTo, updatedContract);
      }

      // 发送事件
      await this.eventBus.publish({
        type: 'contract.approved',
        aggregateId: id,
        data: { contract: updatedContract, approvedBy: approverId, comments },
        timestamp: new Date()
      });

      logger.info('Contract approved', { contractId: id, approverId });
      return updatedContract;
    } catch (error) {
      logger.error('Failed to approve contract', { error, id });
      throw error;
    }
  }

  // 发送合同
  async send(id: string, recipients: string[], message?: string, userId?: string): Promise<Contract> {
    try {
      const contract = await this.findById(id);

      if (contract.status !== 'approved') {
        throw createError.business('Only approved contracts can be sent', 'INVALID_CONTRACT_STATUS');
      }

      // 生成合同PDF
      const contractPdf = await this.generateContractPdf(contract);

      // 发送合同
      await this.notificationService.sendContract(contract, recipients, contractPdf, message);

      // 更新状态
      const updatedContract = await contract.$query().patchAndFetch({
        status: 'sent',
        updatedAt: new Date(),
        metadata: {
          ...contract.metadata,
          sentInfo: {
            sentAt: new Date(),
            sentBy: userId,
            recipients,
            message
          }
        }
      });

      // 记录活动
      await this.addContractActivity(updatedContract, 'sent', 'Contract sent to customer', {
        recipients,
        sentBy: userId,
        message
      });

      // 发送事件
      await this.eventBus.publish({
        type: 'contract.sent',
        aggregateId: id,
        data: { contract: updatedContract, recipients, sentBy: userId },
        timestamp: new Date()
      });

      logger.info('Contract sent', { contractId: id, recipients });
      return updatedContract;
    } catch (error) {
      logger.error('Failed to send contract', { error, id });
      throw error;
    }
  }

  // 电子签名
  async initiateElectronicSignature(id: string, signers: any[], userId?: string): Promise<Contract> {
    try {
      const contract = await this.findById(id);

      if (contract.status !== 'sent') {
        throw createError.business('Only sent contracts can be signed', 'INVALID_CONTRACT_STATUS');
      }

      // 启动电子签名流程
      const signatureRequest = await this.signatureService.initiateSignature(contract, signers);

      // 更新合同
      const updatedContract = await contract.$query().patchAndFetch({
        metadata: {
          ...contract.metadata,
          signatureRequest: {
            requestId: signatureRequest.id,
            initiatedAt: new Date(),
            initiatedBy: userId,
            signers,
            status: 'pending'
          }
        },
        updatedAt: new Date()
      });

      // 记录活动
      await this.addContractActivity(updatedContract, 'signature_initiated', 'Electronic signature initiated', {
        signatureRequestId: signatureRequest.id,
        signers,
        initiatedBy: userId
      });

      // 发送事件
      await this.eventBus.publish({
        type: 'contract.signature_initiated',
        aggregateId: id,
        data: { contract: updatedContract, signatureRequest, initiatedBy: userId },
        timestamp: new Date()
      });

      logger.info('Electronic signature initiated', { contractId: id, signatureRequestId: signatureRequest.id });
      return updatedContract;
    } catch (error) {
      logger.error('Failed to initiate electronic signature', { error, id });
      throw error;
    }
  }

  // 签署合同
  async sign(id: string, signatureData: any, userId?: string): Promise<Contract> {
    try {
      const contract = await this.findById(id);

      // 验证签名
      const isValidSignature = await this.signatureService.validateSignature(contract, signatureData);
      if (!isValidSignature) {
        throw createError.business('Invalid signature', 'INVALID_SIGNATURE');
      }

      // 更新状态
      const updatedContract = await contract.$query().patchAndFetch({
        status: 'signed',
        signedDate: new Date(),
        updatedAt: new Date(),
        metadata: {
          ...contract.metadata,
          signatureInfo: {
            signedAt: new Date(),
            signedBy: userId,
            signatureData
          }
        }
      });

      // 记录活动
      await this.addContractActivity(updatedContract, 'signed', 'Contract signed', {
        signedBy: userId,
        signatureData
      });

      // 激活合同
      if (updatedContract.startDate <= new Date()) {
        await this.activate(id, userId);
      }

      // 发送通知
      if (contract.assignedTo) {
        await this.notificationService.notifyContractSigned(contract.assignedTo, updatedContract);
      }

      // 发送事件
      await this.eventBus.publish({
        type: 'contract.signed',
        aggregateId: id,
        data: { contract: updatedContract, signedBy: userId },
        timestamp: new Date()
      });

      logger.info('Contract signed', { contractId: id });
      return updatedContract;
    } catch (error) {
      logger.error('Failed to sign contract', { error, id });
      throw error;
    }
  }

  // 激活合同
  async activate(id: string, userId?: string): Promise<Contract> {
    try {
      const contract = await this.findById(id);

      if (contract.status !== 'signed') {
        throw createError.business('Only signed contracts can be activated', 'INVALID_CONTRACT_STATUS');
      }

      // 更新状态
      const updatedContract = await contract.$query().patchAndFetch({
        status: 'active',
        updatedAt: new Date()
      });

      // 记录活动
      await this.addContractActivity(updatedContract, 'activated', 'Contract activated', {
        activatedBy: userId
      });

      // 发送事件
      await this.eventBus.publish({
        type: 'contract.activated',
        aggregateId: id,
        data: { contract: updatedContract, activatedBy: userId },
        timestamp: new Date()
      });

      logger.info('Contract activated', { contractId: id });
      return updatedContract;
    } catch (error) {
      logger.error('Failed to activate contract', { error, id });
      throw error;
    }
  }

  // 私有方法
  private async validateAndCleanContractData(data: Partial<IContract>): Promise<Partial<IContract>> {
    const cleaned = { ...data };

    // 验证金额
    if (cleaned.amount !== undefined && cleaned.amount < 0) {
      throw createError.validation('Amount must be non-negative');
    }

    // 验证日期
    if (cleaned.startDate && cleaned.endDate && cleaned.startDate >= cleaned.endDate) {
      throw createError.validation('End date must be after start date');
    }

    return cleaned;
  }

  private async generateContractNumber(type: ContractType): Promise<string> {
    const prefix = type.toUpperCase().substring(0, 3);
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    
    // 获取当月的合同数量
    const count = await Contract.query()
      .where('contract_number', 'like', `${prefix}${year}${month}%`)
      .resultSize();

    const sequence = String(count + 1).padStart(4, '0');
    return `${prefix}${year}${month}${sequence}`;
  }

  private calculateDefaultEndDate(): Date {
    const date = new Date();
    date.setFullYear(date.getFullYear() + 1); // 默认1年期
    return date;
  }

  private generateTermsFromOpportunity(opportunity: any): any[] {
    const terms = [];

    // 产品条款
    if (opportunity.products && opportunity.products.length > 0) {
      terms.push({
        id: 'products',
        title: '产品和服务',
        content: `本合同包含以下产品和服务：\n${opportunity.products.map((p: any) => 
          `- ${p.productName}: 数量 ${p.quantity}, 单价 ${p.unitPrice}, 总价 ${p.totalPrice}`
        ).join('\n')}`,
        type: 'delivery'
      });
    }

    // 付款条款
    terms.push({
      id: 'payment',
      title: '付款条款',
      content: `合同总金额为 ${opportunity.amount} ${opportunity.currency}。付款方式和时间安排将根据双方协商确定。`,
      type: 'payment'
    });

    // 交付条款
    terms.push({
      id: 'delivery',
      title: '交付条款',
      content: '产品和服务的交付时间、地点和方式将根据具体需求确定。',
      type: 'delivery'
    });

    return terms;
  }

  private async applyTemplate(contract: Contract, templateId: string): Promise<void> {
    const template = await this.templateService.getTemplate(templateId);
    if (template) {
      await contract.$query().patch({
        terms: template.terms,
        metadata: {
          ...contract.metadata,
          templateId,
          templateName: template.name,
          appliedAt: new Date()
        }
      });
    }
  }

  private isAllowedUpdateForSignedContract(updateData: Partial<IContract>): boolean {
    // 已签署的合同只允许更新某些字段
    const allowedFields = ['metadata', 'tags'];
    return Object.keys(updateData).every(key => allowedFields.includes(key));
  }

  private async validateContractCompleteness(contract: Contract): Promise<void> {
    const requiredFields = ['title', 'customerId', 'amount', 'currency', 'startDate', 'endDate'];
    
    for (const field of requiredFields) {
      if (!contract[field as keyof Contract]) {
        throw createError.validation(`Required field missing: ${field}`);
      }
    }

    if (!contract.terms || contract.terms.length === 0) {
      throw createError.validation('Contract must have at least one term');
    }
  }

  private async generateContractPdf(contract: Contract): Promise<Buffer> {
    // 这里应该使用PDF生成库生成合同PDF
    // 返回模拟的PDF数据
    return Buffer.from('Contract PDF content');
  }

  private async addContractActivity(contract: Contract, type: string, description: string, metadata?: any): Promise<void> {
    // 这里应该添加到合同活动表
    logger.info('Contract activity added', {
      contractId: contract.id,
      type,
      description,
      metadata
    });
  }

  private getChanges(oldData: any, newData: any): Record<string, { old: any; new: any }> {
    const changes: Record<string, { old: any; new: any }> = {};
    
    Object.keys(newData).forEach(key => {
      if (JSON.stringify(oldData[key]) !== JSON.stringify(newData[key])) {
        changes[key] = {
          old: oldData[key],
          new: newData[key]
        };
      }
    });
    
    return changes;
  }
}
