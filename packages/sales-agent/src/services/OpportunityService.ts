import { Opportunity } from '../models/Opportunity';
import { OpportunityForecastService } from './OpportunityForecastService';
import { SalesProcessService } from './SalesProcessService';
import { CompetitorAnalysisService } from './CompetitorAnalysisService';
import { NotificationService } from './NotificationService';
import { EventBus } from '../utils/event-bus';
import { logger } from '../utils/logger';
import { 
  Opportunity as IOpportunity, 
  OpportunityStage, 
  QueryParams,
  PaginatedResult 
} from '@linkagent/shared/types';
import { createError } from '../middleware/error-handler';

export class OpportunityService {
  private forecastService: OpportunityForecastService;
  private salesProcessService: SalesProcessService;
  private competitorService: CompetitorAnalysisService;
  private notificationService: NotificationService;
  private eventBus: EventBus;

  constructor() {
    this.forecastService = new OpportunityForecastService();
    this.salesProcessService = new SalesProcessService();
    this.competitorService = new CompetitorAnalysisService();
    this.notificationService = new NotificationService();
    this.eventBus = EventBus.getInstance();
  }

  // 创建商机
  async create(opportunityData: Partial<IOpportunity>, userId?: string): Promise<Opportunity> {
    try {
      logger.info('Creating new opportunity', { opportunityData, userId });

      // 数据验证和清洗
      const cleanedData = await this.validateAndCleanOpportunityData(opportunityData);

      // 设置默认值
      const defaultData = {
        stage: 'prospecting' as OpportunityStage,
        probability: 10,
        tags: [],
        metadata: {},
        stageHistory: [{
          stage: cleanedData.stage || 'prospecting',
          changedAt: new Date(),
          changedBy: userId || 'system'
        }],
        salesProcess: await this.salesProcessService.initializeProcess(cleanedData),
        forecastCategory: 'pipeline',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // 创建商机
      const opportunity = await Opportunity.query().insert({
        ...defaultData,
        ...cleanedData
      });

      // 初始化销售流程
      await this.salesProcessService.startProcess(opportunity);

      // 竞争对手分析
      if (cleanedData.competitorInfo) {
        await this.competitorService.analyzeCompetitors(opportunity, cleanedData.competitorInfo);
      }

      // 发送事件
      await this.eventBus.publish({
        type: 'opportunity.created',
        aggregateId: opportunity.id,
        data: { opportunity, createdBy: userId },
        timestamp: new Date()
      });

      // 发送通知
      if (opportunity.assignedTo) {
        await this.notificationService.notifyNewOpportunity(opportunity.assignedTo, opportunity);
      }

      logger.info('Opportunity created successfully', { opportunityId: opportunity.id });
      return opportunity;
    } catch (error) {
      logger.error('Failed to create opportunity', { error, opportunityData });
      throw error;
    }
  }

  // 从线索创建商机
  async createFromLead(lead: any, opportunityData: Partial<IOpportunity>, userId?: string): Promise<Opportunity> {
    try {
      logger.info('Creating opportunity from lead', { leadId: lead.id, opportunityData, userId });

      // 从线索数据映射到商机数据
      const mappedData = {
        customerId: lead.customerId,
        leadId: lead.id,
        name: opportunityData.name || `Opportunity from ${lead.title}`,
        description: opportunityData.description || lead.description,
        amount: opportunityData.amount || lead.estimatedValue || 0,
        currency: opportunityData.currency || lead.currency || 'CNY',
        expectedCloseDate: opportunityData.expectedCloseDate || this.calculateExpectedCloseDate(),
        assignedTo: opportunityData.assignedTo || lead.assignedTo,
        source: lead.source,
        tags: [...(lead.tags || []), ...(opportunityData.tags || [])],
        metadata: {
          ...lead.metadata,
          ...opportunityData.metadata,
          convertedFromLead: {
            leadId: lead.id,
            convertedAt: new Date(),
            convertedBy: userId
          }
        },
        // 从线索数据推断利益相关者
        stakeholders: this.extractStakeholdersFromLead(lead),
        // 从线索数据推断决策标准
        decisionCriteria: lead.painPoints || [],
        // 设置初始产品信息
        products: opportunityData.products || []
      };

      const opportunity = await this.create(mappedData, userId);

      // 更新预测
      await this.forecastService.updateForecast(opportunity);

      logger.info('Opportunity created from lead successfully', { 
        opportunityId: opportunity.id, 
        leadId: lead.id 
      });

      return opportunity;
    } catch (error) {
      logger.error('Failed to create opportunity from lead', { error, leadId: lead.id });
      throw error;
    }
  }

  // 获取商机列表
  async findAll(params: QueryParams): Promise<PaginatedResult<Opportunity>> {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'expected_close_date',
        sortOrder = 'asc',
        search,
        filters = {}
      } = params;

      let query = Opportunity.query()
        .withGraphFetched('[customer, assignedUser, lead]');

      // 搜索
      if (search) {
        query = query.where(builder => {
          builder
            .where('name', 'ilike', `%${search}%`)
            .orWhere('description', 'ilike', `%${search}%`);
        });
      }

      // 过滤器
      if (filters.stage) {
        query = query.where('stage', filters.stage);
      }
      if (filters.assignedTo) {
        query = query.where('assigned_to', filters.assignedTo);
      }
      if (filters.customerId) {
        query = query.where('customer_id', filters.customerId);
      }
      if (filters.forecastCategory) {
        query = query.where('forecast_category', filters.forecastCategory);
      }
      if (filters.amountMin) {
        query = query.where('amount', '>=', filters.amountMin);
      }
      if (filters.amountMax) {
        query = query.where('amount', '<=', filters.amountMax);
      }
      if (filters.probabilityMin) {
        query = query.where('probability', '>=', filters.probabilityMin);
      }
      if (filters.probabilityMax) {
        query = query.where('probability', '<=', filters.probabilityMax);
      }
      if (filters.expectedCloseAfter) {
        query = query.where('expected_close_date', '>=', filters.expectedCloseAfter);
      }
      if (filters.expectedCloseBefore) {
        query = query.where('expected_close_date', '<=', filters.expectedCloseBefore);
      }
      if (filters.isOverdue) {
        query = query.where('expected_close_date', '<', new Date())
          .where('stage', 'not in', ['closed_won', 'closed_lost']);
      }
      if (filters.isStale) {
        const staleDate = new Date();
        staleDate.setDate(staleDate.getDate() - (filters.staleDays || 30));
        query = query.where('last_activity_at', '<', staleDate)
          .where('stage', 'not in', ['closed_won', 'closed_lost']);
      }

      // 排序
      query = query.orderBy(sortBy, sortOrder);

      // 分页
      const result = await query.page(page - 1, limit);

      return {
        data: result.results,
        total: result.total,
        page,
        limit,
        totalPages: Math.ceil(result.total / limit),
        hasNext: page < Math.ceil(result.total / limit),
        hasPrev: page > 1
      };
    } catch (error) {
      logger.error('Failed to fetch opportunities', { error, params });
      throw error;
    }
  }

  // 获取单个商机
  async findById(id: string): Promise<Opportunity> {
    try {
      const opportunity = await Opportunity.query()
        .findById(id)
        .withGraphFetched('[customer, assignedUser, lead, quotes, contracts, activities, notes]');

      if (!opportunity) {
        throw createError.notFound('Opportunity', id);
      }

      return opportunity;
    } catch (error) {
      logger.error('Failed to fetch opportunity', { error, id });
      throw error;
    }
  }

  // 更新商机
  async update(id: string, updateData: Partial<IOpportunity>, userId?: string): Promise<Opportunity> {
    try {
      logger.info('Updating opportunity', { id, updateData, userId });

      const opportunity = await this.findById(id);
      const oldData = { ...opportunity };

      // 数据验证和清洗
      const cleanedData = await this.validateAndCleanOpportunityData(updateData);

      // 检查阶段变化
      if (cleanedData.stage && cleanedData.stage !== opportunity.stage) {
        await opportunity.moveToStage(cleanedData.stage, userId || 'system');
        delete cleanedData.stage; // 已经在moveToStage中处理
      }

      // 更新商机
      const updatedOpportunity = await opportunity.$query().patchAndFetch({
        ...cleanedData,
        updatedAt: new Date()
      });

      // 记录活动
      await updatedOpportunity.addActivity('updated', 'Opportunity information updated', {
        changes: this.getChanges(oldData, cleanedData),
        updatedBy: userId
      });

      // 更新预测
      if (this.shouldUpdateForecast(oldData, cleanedData)) {
        await this.forecastService.updateForecast(updatedOpportunity);
      }

      // 发送事件
      await this.eventBus.publish({
        type: 'opportunity.updated',
        aggregateId: id,
        data: { 
          opportunity: updatedOpportunity, 
          oldData, 
          changes: this.getChanges(oldData, cleanedData),
          updatedBy: userId 
        },
        timestamp: new Date()
      });

      logger.info('Opportunity updated successfully', { opportunityId: id });
      return updatedOpportunity;
    } catch (error) {
      logger.error('Failed to update opportunity', { error, id, updateData });
      throw error;
    }
  }

  // 删除商机
  async delete(id: string, userId?: string): Promise<void> {
    try {
      logger.info('Deleting opportunity', { id, userId });

      const opportunity = await this.findById(id);

      // 软删除
      await opportunity.$query().patch({
        deletedAt: new Date(),
        updatedAt: new Date()
      });

      // 发送事件
      await this.eventBus.publish({
        type: 'opportunity.deleted',
        aggregateId: id,
        data: { opportunity, deletedBy: userId },
        timestamp: new Date()
      });

      logger.info('Opportunity deleted successfully', { opportunityId: id });
    } catch (error) {
      logger.error('Failed to delete opportunity', { error, id });
      throw error;
    }
  }

  // 推进到下一阶段
  async advanceStage(id: string, userId?: string): Promise<Opportunity> {
    try {
      const opportunity = await this.findById(id);
      const nextStage = this.getNextStage(opportunity.stage);

      if (!nextStage) {
        throw createError.business('Cannot advance from current stage', 'STAGE_ADVANCEMENT_NOT_ALLOWED');
      }

      await opportunity.moveToStage(nextStage, userId || 'system');

      // 更新预测
      await this.forecastService.updateForecast(opportunity);

      // 发送通知
      if (opportunity.assignedTo) {
        await this.notificationService.notifyStageChange(opportunity.assignedTo, opportunity, nextStage);
      }

      logger.info('Opportunity stage advanced', { opportunityId: id, newStage: nextStage });
      return await this.findById(id);
    } catch (error) {
      logger.error('Failed to advance opportunity stage', { error, id });
      throw error;
    }
  }

  // 赢得商机
  async win(id: string, reason?: string, userId?: string): Promise<Opportunity> {
    try {
      const opportunity = await this.findById(id);
      await opportunity.win(reason, userId);

      // 更新预测
      await this.forecastService.updateForecast(opportunity);

      // 发送事件
      await this.eventBus.publish({
        type: 'opportunity.won',
        aggregateId: id,
        data: { opportunity, reason, wonBy: userId },
        timestamp: new Date()
      });

      // 发送通知
      if (opportunity.assignedTo) {
        await this.notificationService.notifyOpportunityWon(opportunity.assignedTo, opportunity);
      }

      logger.info('Opportunity won', { opportunityId: id, reason });
      return await this.findById(id);
    } catch (error) {
      logger.error('Failed to win opportunity', { error, id });
      throw error;
    }
  }

  // 丢失商机
  async lose(id: string, reason?: string, userId?: string): Promise<Opportunity> {
    try {
      const opportunity = await this.findById(id);
      await opportunity.lose(reason, userId);

      // 更新预测
      await this.forecastService.updateForecast(opportunity);

      // 发送事件
      await this.eventBus.publish({
        type: 'opportunity.lost',
        aggregateId: id,
        data: { opportunity, reason, lostBy: userId },
        timestamp: new Date()
      });

      // 发送通知
      if (opportunity.assignedTo) {
        await this.notificationService.notifyOpportunityLost(opportunity.assignedTo, opportunity);
      }

      logger.info('Opportunity lost', { opportunityId: id, reason });
      return await this.findById(id);
    } catch (error) {
      logger.error('Failed to lose opportunity', { error, id });
      throw error;
    }
  }

  // 获取销售漏斗数据
  async getSalesFunnel(filters?: any): Promise<any> {
    try {
      let query = Opportunity.query();

      // 应用过滤器
      if (filters?.assignedTo) {
        query = query.where('assigned_to', filters.assignedTo);
      }
      if (filters?.dateFrom) {
        query = query.where('created_at', '>=', filters.dateFrom);
      }
      if (filters?.dateTo) {
        query = query.where('created_at', '<=', filters.dateTo);
      }

      const results = await query
        .groupBy('stage')
        .count('* as count')
        .sum('amount as totalAmount')
        .avg('probability as avgProbability')
        .select('stage');

      const funnel = {
        prospecting: { count: 0, amount: 0, probability: 0 },
        qualification: { count: 0, amount: 0, probability: 0 },
        proposal: { count: 0, amount: 0, probability: 0 },
        negotiation: { count: 0, amount: 0, probability: 0 },
        closed_won: { count: 0, amount: 0, probability: 0 },
        closed_lost: { count: 0, amount: 0, probability: 0 }
      };

      results.forEach(result => {
        funnel[result.stage as keyof typeof funnel] = {
          count: parseInt(result.count),
          amount: parseFloat(result.totalAmount) || 0,
          probability: Math.round(parseFloat(result.avgProbability) || 0)
        };
      });

      return funnel;
    } catch (error) {
      logger.error('Failed to get sales funnel', { error, filters });
      throw error;
    }
  }

  // 私有方法
  private async validateAndCleanOpportunityData(data: Partial<IOpportunity>): Promise<Partial<IOpportunity>> {
    const cleaned = { ...data };

    // 验证金额
    if (cleaned.amount !== undefined && cleaned.amount < 0) {
      throw createError.validation('Amount must be non-negative');
    }

    // 验证概率
    if (cleaned.probability !== undefined && (cleaned.probability < 0 || cleaned.probability > 100)) {
      throw createError.validation('Probability must be between 0 and 100');
    }

    // 清理标签
    if (cleaned.tags) {
      cleaned.tags = cleaned.tags.map(tag => tag.trim().toLowerCase()).filter(Boolean);
    }

    return cleaned;
  }

  private calculateExpectedCloseDate(): Date {
    const date = new Date();
    date.setDate(date.getDate() + 90); // 默认90天后
    return date;
  }

  private extractStakeholdersFromLead(lead: any): any[] {
    const stakeholders = [];

    if (lead.contactInfo?.email) {
      stakeholders.push({
        name: lead.metadata?.inferredName?.firstName && lead.metadata?.inferredName?.lastName 
          ? `${lead.metadata.inferredName.firstName} ${lead.metadata.inferredName.lastName}`
          : 'Primary Contact',
        role: lead.jobTitle || 'Contact',
        influence: lead.decisionMaker ? 'high' : 'medium',
        sentiment: 'neutral',
        contactInfo: lead.contactInfo
      });
    }

    return stakeholders;
  }

  private getNextStage(currentStage: OpportunityStage): OpportunityStage | null {
    const stageFlow: Record<OpportunityStage, OpportunityStage | null> = {
      prospecting: 'qualification',
      qualification: 'proposal',
      proposal: 'negotiation',
      negotiation: 'closed_won',
      closed_won: null,
      closed_lost: null
    };

    return stageFlow[currentStage];
  }

  private shouldUpdateForecast(oldData: any, newData: any): boolean {
    const forecastFields = ['stage', 'probability', 'amount', 'expectedCloseDate'];
    return forecastFields.some(field => 
      JSON.stringify(oldData[field]) !== JSON.stringify(newData[field])
    );
  }

  private getChanges(oldData: any, newData: any): Record<string, { old: any; new: any }> {
    const changes: Record<string, { old: any; new: any }> = {};
    
    Object.keys(newData).forEach(key => {
      if (JSON.stringify(oldData[key]) !== JSON.stringify(newData[key])) {
        changes[key] = {
          old: oldData[key],
          new: newData[key]
        };
      }
    });
    
    return changes;
  }
}
